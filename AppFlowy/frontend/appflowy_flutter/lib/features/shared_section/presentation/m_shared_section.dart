import 'package:appflowy/features/shared_section/data/repositories/rust_shared_pages_repository_impl.dart';
import 'package:appflowy/features/shared_section/logic/shared_section_bloc.dart';
import 'package:appflowy/features/shared_section/presentation/widgets/m_shared_page_list.dart';
import 'package:appflowy/features/shared_section/presentation/widgets/m_shared_section_header.dart';
import 'package:appflowy/features/shared_section/presentation/widgets/refresh_button.dart';
import 'package:appflowy/features/shared_section/presentation/widgets/shared_section_empty.dart';
import 'package:appflowy/features/shared_section/presentation/widgets/shared_section_error.dart';
import 'package:appflowy/features/shared_section/presentation/widgets/shared_section_loading.dart';
import 'package:appflowy/mobile/application/mobile_router.dart';
import 'package:appflowy/shared/icon_emoji_picker/tab.dart';
import 'package:appflowy/workspace/presentation/home/<USER>';
import 'package:flowy_infra_ui/widget/spacing.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MSharedSection extends StatelessWidget {
  const MSharedSection({
    super.key,
    required this.workspaceId,
  });

  final String workspaceId;

  @override
  Widget build(BuildContext context) {
    final repository = RustSharePagesRepositoryImpl();

    return BlocProvider(
      create: (_) => SharedSectionBloc(
        workspaceId: workspaceId,
        repository: repository,
        enablePolling: true,
      )..add(const SharedSectionInitEvent()),
      child: BlocBuilder<SharedSectionBloc, SharedSectionState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const SharedSectionLoading();
          }

          if (state.errorMessage.isNotEmpty) {
            return SharedSectionError(errorMessage: state.errorMessage);
          }

          // hide the shared section if there are no shared pages
          if (state.sharedPages.isEmpty) {
            return const SharedSectionEmpty();
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const VSpace(HomeSpaceViewSizes.mVerticalPadding),

              // Shared header
              MSharedSectionHeader(),

              Padding(
                padding: const EdgeInsets.only(
                  left: HomeSpaceViewSizes.mHorizontalPadding,
                ),
                child: MSharedPageList(
                  sharedPages: state.sharedPages,
                  onSelected: (view) {
                    context.pushView(
                      view,
                      tabs: [
                        PickerTabType.emoji,
                        PickerTabType.icon,
                        PickerTabType.custom,
                      ].map((e) => e.name).toList(),
                    );
                  },
                ),
              ),

              // Refresh button, for debugging only
              if (kDebugMode)
                RefreshSharedSectionButton(
                  onTap: () {
                    context.read<SharedSectionBloc>().add(
                          const SharedSectionEvent.refresh(),
                        );
                  },
                ),
            ],
          );
        },
      ),
    );
  }
}
