import 'dart:io';

import 'package:appflowy/features/workspace/data/repositories/rust_workspace_repository_impl.dart';
import 'package:appflowy/features/workspace/logic/workspace_bloc.dart';
import 'package:appflowy/mobile/presentation/favorite/mobile_favorite_folder.dart';
import 'package:appflowy/mobile/presentation/home/<USER>';
import 'package:appflowy/startup/startup.dart';
import 'package:appflowy/user/application/auth/auth_service.dart';
import 'package:appflowy/workspace/presentation/home/<USER>/workspace_failed_screen.dart';
import 'package:appflowy_backend/dispatch/dispatch.dart';
import 'package:appflowy_backend/protobuf/flowy-folder/workspace.pb.dart';
import 'package:appflowy_backend/protobuf/flowy-user/protobuf.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MobileFavoriteScreen extends StatelessWidget {
  const MobileFavoriteScreen({
    super.key,
  });

  static const routeName = '/favorite';

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: Future.wait([
        FolderEventGetCurrentWorkspaceSetting().send(),
        getIt<AuthService>().getUser(),
      ]),
      builder: (context, snapshots) {
        if (!snapshots.hasData) {
          return const Center(child: CircularProgressIndicator.adaptive());
        }

        final latest = snapshots.data?[0].fold(
          (latest) {
            return latest as WorkspaceLatestPB?;
          },
          (error) => null,
        );
        final userProfile = snapshots.data?[1].fold(
          (userProfilePB) {
            return userProfilePB as UserProfilePB?;
          },
          (error) => null,
        );

        // In the unlikely case either of the above is null, eg.
        // when a workspace is already open this can happen.
        if (latest == null || userProfile == null) {
          return const WorkspaceFailedScreen();
        }

        return Scaffold(
          body: SafeArea(
            child: BlocProvider(
              create: (_) => UserWorkspaceBloc(
                userProfile: userProfile,
                repository: RustWorkspaceRepositoryImpl(
                  userId: userProfile.id,
                ),
              )..add(
                  UserWorkspaceEvent.initialize(),
                ),
              child: BlocBuilder<UserWorkspaceBloc, UserWorkspaceState>(
                buildWhen: (previous, current) =>
                    previous.currentWorkspace?.workspaceId !=
                    current.currentWorkspace?.workspaceId,
                builder: (context, state) {
                  return MobileFavoritePage(
                    userProfile: userProfile,
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }
}

class MobileFavoritePage extends StatelessWidget {
  const MobileFavoritePage({
    super.key,
    required this.userProfile,
  });

  final UserProfilePB userProfile;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header
        Padding(
          padding: EdgeInsets.only(
            left: 16,
            right: 16,
            top: Platform.isAndroid ? 8.0 : 0.0,
          ),
          child: MobileHomePageHeader(
            userProfile: userProfile,
          ),
        ),
        const Divider(),

        // Folder
        Expanded(
          child: MobileFavoritePageFolder(
            userProfile: userProfile,
          ),
        ),
      ],
    );
  }
}
