PODS:
  - app_links (0.0.2):
    - Flutter
  - appflowy_backend (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - flowy_infra_ui (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - image_picker_ios (0.0.1):
    - Flutter
  - integration_test (0.0.1):
    - Flutter
  - irondash_engine_context (0.0.1):
    - Flutter
  - keyboard_height_plugin (0.0.1):
    - Flutter
  - open_filex (0.0.2):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - ReachabilitySwift (5.2.4)
  - saver_gallery (0.0.1):
    - Flutter
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - super_native_extensions (0.0.1):
    - Flutter
  - SwiftyGif (5.4.5)
  - Toast (4.1.1)
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - appflowy_backend (from `.symlinks/plugins/appflowy_backend/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - flowy_infra_ui (from `.symlinks/plugins/flowy_infra_ui/ios`)
  - Flutter (from `Flutter`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - integration_test (from `.symlinks/plugins/integration_test/ios`)
  - irondash_engine_context (from `.symlinks/plugins/irondash_engine_context/ios`)
  - keyboard_height_plugin (from `.symlinks/plugins/keyboard_height_plugin/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - saver_gallery (from `.symlinks/plugins/saver_gallery/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - super_native_extensions (from `.symlinks/plugins/super_native_extensions/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - ReachabilitySwift
    - SDWebImage
    - SwiftyGif
    - Toast

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  appflowy_backend:
    :path: ".symlinks/plugins/appflowy_backend/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  flowy_infra_ui:
    :path: ".symlinks/plugins/flowy_infra_ui/ios"
  Flutter:
    :path: Flutter
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  integration_test:
    :path: ".symlinks/plugins/integration_test/ios"
  irondash_engine_context:
    :path: ".symlinks/plugins/irondash_engine_context/ios"
  keyboard_height_plugin:
    :path: ".symlinks/plugins/keyboard_height_plugin/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  saver_gallery:
    :path: ".symlinks/plugins/saver_gallery/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  super_native_extensions:
    :path: ".symlinks/plugins/super_native_extensions/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  app_links: 3da4c36b46cac3bf24eb897f1a6ce80bda109874
  appflowy_backend: 78f6a053f756e6bc29bcc5a2106cbe77b756e97a
  connectivity_plus: 481668c94744c30c53b8895afb39159d1e619bdf
  device_info_plus: 21fcca2080fbcd348be798aa36c3e5ed849eefbe
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: 9b3292d7c8bc68c8a7bf8eb78f730e49c8efc517
  flowy_infra_ui: 931b73a18b54a392ab6152eebe29a63a30751f53
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  fluttertoast: 76fea30fcf04176325f6864c87306927bd7d2038
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  integration_test: 4a889634ef21a45d28d50d622cf412dc6d9f586e
  irondash_engine_context: 8e58ca8e0212ee9d1c7dc6a42121849986c88486
  keyboard_height_plugin: ef70a8181b29f27670e9e2450814ca6b6dc05b05
  open_filex: 432f3cd11432da3e39f47fcc0df2b1603854eff1
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  saver_gallery: af2d0c762dafda254e0ad025ef0dabd6506cd490
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  super_native_extensions: b763c02dc3a8fd078389f410bf15149179020cb4
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  webview_flutter_wkwebview: 44d4dee7d7056d5ad185d25b38404436d56c547c

PODFILE CHECKSUM: d0d9b4ff572d8695c38eb3f9b490f55cdfc57eca

COCOAPODS: 1.16.2
