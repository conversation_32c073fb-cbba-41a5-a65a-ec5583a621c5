<manifest xmlns:android="http://schemas.android.com/apk/res/android">
  <application android:label="AppFlowy" android:icon="@mipmap/ic_launcher"
    android:name="${applicationName}">
    <activity android:name=".MainActivity"
      android:exported="true"
      android:launchMode="singleInstance"
      android:theme="@style/LaunchTheme"
      android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
      android:hardwareAccelerated="true" android:windowSoftInputMode="adjustResize">
      <!--
			Specifies an Android theme to apply to this Activity as soon as
			the Android process has started. This theme is visible to the user
			while the Flutter UI initializes. After that, this theme continues
			to determine the Window background behind the Flutter UI.
			-->
      <meta-data android:name="io.flutter.embedding.android.NormalTheme"
        android:resource="@style/NormalTheme" />
      <!--
			Displays an Android View that continues showing the launch screen
			Drawable until Flutter paints its first frame, then this splash
			screen fades out. A splash screen is useful to avoid any visual
			gap between the end of Android's launch screen and the painting of
			Flutter's first frame.
			-->
      <meta-data android:name="io.flutter.embedding.android.SplashScreenDrawable"
        android:resource="@drawable/launch_background" />
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
      <meta-data android:name="flutter_deeplinking_enabled" android:value="true" />
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="http" />
        <data android:scheme="https" />
        <data android:scheme="appflowy-flutter" />
      </intent-filter>
    </activity>
    <!--
		Don't delete the meta-data below.
		This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
		-->
    <meta-data android:name="flutterEmbedding" android:value="2" />
    <meta-data android:name="io.flutter.embedding.android.EnableImpeller"
      android:value="false" />
  </application>
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <!-- Permission to read files from external storage (outside application container).
  As of Android 12 this permission no longer has any effect. Instead use the
  READ_MEDIA_IMAGES, READ_MEDIA_VIDEO or READM_MEDIA_AUDIO permissions. -->
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
    android:maxSdkVersion="32" />
  <!-- Permissions to read media files. -->
  <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
  <queries>
    <intent>
      <action android:name="android.support.customtabs.action.CustomTabsService" />
    </intent>
  </queries>
  <!--
    Media access permissions.
    Android 13 or higher.
    Used for VideoBlock (edia_kit)
  -->
  <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
  <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
  <uses-permission android:name="android.permission.CAMERA" />
</manifest>