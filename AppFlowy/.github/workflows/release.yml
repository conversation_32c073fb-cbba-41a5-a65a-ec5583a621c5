name: release

on:
  push:
    tags:
      - "*"

env:
  FLUTTER_VERSION: "3.27.4"
  RUST_TOOLCHAIN: "1.85.0"

jobs:
  create-release:
    runs-on: ubuntu-latest
    env:
      RELEASE_NOTES_PATH: /tmp/release_notes
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Build release notes
        run: |
          touch ${{ env.RELEASE_NOTES_PATH }}
          cat CHANGELOG.md | sed -e '/./{H;$!d;}' -e "x;/##\ Version\ ${{ github.ref_name }}/"'!d;' >> ${{ env.RELEASE_NOTES_PATH }}

      - name: Create release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: v${{ github.ref }}
          body_path: ${{ env.RELEASE_NOTES_PATH }}

  # the package name should be with the format: AppFlowy-<version>-<os>-<arch>

  build-for-windows:
    name: ${{ matrix.job.target }} (${{ matrix.job.os }})
    needs: create-release
    env:
      WINDOWS_APP_RELEASE_PATH: frontend\appflowy_flutter\product\${{ github.ref_name }}\windows
      WINDOWS_ZIP_NAME: AppFlowy-${{ github.ref_name }}-windows-x86_64.zip
      WINDOWS_INSTALLER_NAME: AppFlowy-${{ github.ref_name }}-windows-x86_64
    runs-on: ${{ matrix.job.os }}
    strategy:
      fail-fast: false
      matrix:
        job:
          - { target: x86_64-pc-windows-msvc, os: windows-2019 }
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Install flutter
        uses: subosito/flutter-action@v2
        with:
          channel: "stable"
          flutter-version: ${{ env.FLUTTER_VERSION }}

      - name: Install Rust toolchain
        uses: actions-rs/toolchain@v1
        with:
          toolchain: ${{ env.RUST_TOOLCHAIN }}
          target: ${{ matrix.job.target }}
          override: true
          components: rustfmt
          profile: minimal

      - name: Install prerequisites
        working-directory: frontend
        run: |
          vcpkg integrate install
          cargo install --force --locked cargo-make
          cargo install --force --locked duckscript_cli

      - name: Build Windows app
        working-directory: frontend
        # the cargo make script has to be run separately because of file locking issues
        run: |
          flutter config --enable-windows-desktop
          dart ./scripts/flutter_release_build/build_flowy.dart exclude-directives . ${{ github.ref_name }}
          cargo make --env APP_VERSION=${{ github.ref_name }} --profile production-windows-x86 appflowy
          dart ./scripts/flutter_release_build/build_flowy.dart include-directives . ${{ github.ref_name }}

      - name: Archive Asset
        uses: vimtor/action-zip@v1
        with:
          files: ${{ env.WINDOWS_APP_RELEASE_PATH }}\
          dest: ${{ env.WINDOWS_APP_RELEASE_PATH }}\${{ env.WINDOWS_ZIP_NAME }}

      - name: Copy installer config & icon file
        working-directory: frontend
        run: |
          cp scripts/windows_installer/* ../${{ env.WINDOWS_APP_RELEASE_PATH }}

      - name: Build installer executable
        working-directory: ${{ env.WINDOWS_APP_RELEASE_PATH }}
        run: |
          iscc /F${{ env.WINDOWS_INSTALLER_NAME }} inno_setup_config.iss /DAppVersion=${{ github.ref_name }}

      - name: Upload Asset
        id: upload-release-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ${{ env.WINDOWS_APP_RELEASE_PATH }}\${{ env.WINDOWS_ZIP_NAME }}
          asset_name: ${{ env.WINDOWS_ZIP_NAME }}
          asset_content_type: application/octet-stream

      - name: Upload Installer Asset
        id: upload-installer-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ${{ env.WINDOWS_APP_RELEASE_PATH }}\Output\${{ env.WINDOWS_INSTALLER_NAME }}.exe
          asset_name: ${{ env.WINDOWS_INSTALLER_NAME }}.exe
          asset_content_type: application/octet-stream

  build-for-macOS-x86_64:
    name: ${{ matrix.job.target }} (${{ matrix.job.os }}) [${{ matrix.job.extra-build-args }}]
    runs-on: ${{ matrix.job.os }}
    needs: create-release
    env:
      MACOS_APP_RELEASE_PATH: frontend/appflowy_flutter/product/${{ github.ref_name }}/macos/Release
      MACOS_X86_ZIP_NAME: AppFlowy-${{ github.ref_name }}-macos-x86_64.zip
      MACOS_DMG_NAME: AppFlowy-${{ github.ref_name }}-macos-x86_64
    strategy:
      fail-fast: false
      matrix:
        job:
          - { target: x86_64-apple-darwin, os: macos-13, extra-build-args: "" }
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Install flutter
        uses: subosito/flutter-action@v2
        with:
          channel: "stable"
          flutter-version: ${{ env.FLUTTER_VERSION }}

      - name: Install Rust toolchain
        uses: actions-rs/toolchain@v1
        with:
          toolchain: ${{ env.RUST_TOOLCHAIN }}
          target: ${{ matrix.job.target }}
          override: true
          components: rustfmt
          profile: minimal

      - name: Install prerequisites
        working-directory: frontend
        run: |
          cargo install --force --locked cargo-make
          cargo install --force --locked duckscript_cli

      - name: Build AppFlowy
        working-directory: frontend
        run: |
          flutter config --enable-macos-desktop
          dart ./scripts/flutter_release_build/build_flowy.dart run . ${{ github.ref_name }}

      - name: Codesign AppFlowy
        run: |
          echo ${{ secrets.MACOS_CERTIFICATE }} | base64 --decode > certificate.p12
          security create-keychain -p action build.keychain
          security default-keychain -s build.keychain
          security unlock-keychain -p action build.keychain
          security import certificate.p12 -k build.keychain -P ${{ secrets.MACOS_CERTIFICATE_PWD }} -T /usr/bin/codesign
          security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k action build.keychain
          /usr/bin/codesign --force --options runtime --deep --sign "${{ secrets.MACOS_CODESIGN_ID }}" "${{ env.MACOS_APP_RELEASE_PATH }}/AppFlowy.app" -v

      - name: Create macOS dmg
        run: |
          brew install create-dmg
          i=0
          until [[ -e "${{ env.MACOS_APP_RELEASE_PATH }}/${{ env.MACOS_DMG_NAME }}.dmg" ]]; do
            create-dmg \
            --volname ${{ env.MACOS_DMG_NAME }} \
            --hide-extension "AppFlowy.app" \
            --background frontend/scripts/dmg_assets/AppFlowyInstallerBackground.jpg \
            --window-size 600 450 \
            --icon-size 94 \
            --icon "AppFlowy.app" 141 249 \
            --app-drop-link 458 249 \
            "${{ env.MACOS_APP_RELEASE_PATH }}/${{ env.MACOS_DMG_NAME }}.dmg" \
            "${{ env.MACOS_APP_RELEASE_PATH }}/AppFlowy.app" || true
            if [[ $i -eq 10 ]]; then
              echo 'Error: create-dmg did not succeed even after 10 tries.'
              exit 1
            fi
            i=$((i+1))
          done
      - name: Notarize AppFlowy
        run: |
          xcrun notarytool submit ${{ env.MACOS_APP_RELEASE_PATH }}/${{ env.MACOS_DMG_NAME }}.dmg --apple-id ${{ secrets.MACOS_NOTARY_USER }} --team-id ${{ secrets.MACOS_TEAM_ID }} --password ${{ secrets.MACOS_NOTARY_PWD }} -v -f "json" --wait

      - name: Archive Asset
        working-directory: ${{ env.MACOS_APP_RELEASE_PATH }}
        run: zip --symlinks -qr ${{ env.MACOS_X86_ZIP_NAME }} AppFlowy.app

      - name: Upload Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ${{ env.MACOS_APP_RELEASE_PATH }}/${{ env.MACOS_X86_ZIP_NAME }}
          asset_name: ${{ env.MACOS_X86_ZIP_NAME }}
          asset_content_type: application/octet-stream

      - name: Upload DMG Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ${{ env.MACOS_APP_RELEASE_PATH }}/${{ env.MACOS_DMG_NAME }}.dmg
          asset_name: ${{ env.MACOS_DMG_NAME }}.dmg
          asset_content_type: application/octet-stream

  build-for-macOS-universal:
    name: ${{ matrix.job.target }} (${{ matrix.job.os }}) [${{ matrix.job.extra-build-args }}]
    runs-on: ${{ matrix.job.os }}
    needs: create-release
    env:
      MACOS_APP_RELEASE_PATH: frontend/appflowy_flutter/product/${{ github.ref_name }}/macos/Release
      MACOS_AARCH64_ZIP_NAME: AppFlowy-${{ github.ref_name }}-macos-universal.zip
      MACOS_DMG_NAME: AppFlowy-${{ github.ref_name }}-macos-universal
    strategy:
      fail-fast: false
      matrix:
        job:
          - {
            targets: "aarch64-apple-darwin,x86_64-apple-darwin",
            os: macos-14,
            extra-build-args: "",
          }
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Install flutter
        uses: subosito/flutter-action@v2
        with:
          channel: "stable"
          flutter-version: ${{ env.FLUTTER_VERSION }}

      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: ${{ env.RUST_TOOLCHAIN }}
          targets: ${{ matrix.job.targets }}
          components: rustfmt

      - name: Install prerequisites
        working-directory: frontend
        run: |
          cargo install --force --locked cargo-make
          cargo install --force --locked duckscript_cli

      - name: Build AppFlowy
        working-directory: frontend
        run: |
          flutter config --enable-macos-desktop
          sh scripts/flutter_release_build/build_universal_package_for_macos.sh ${{ github.ref_name }}

      - name: Codesign AppFlowy
        run: |
          echo ${{ secrets.MACOS_CERTIFICATE }} | base64 --decode > certificate.p12
          security create-keychain -p action build.keychain
          security default-keychain -s build.keychain
          security unlock-keychain -p action build.keychain
          security import certificate.p12 -k build.keychain -P ${{ secrets.MACOS_CERTIFICATE_PWD }} -T /usr/bin/codesign
          security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k action build.keychain
          /usr/bin/codesign --force --options runtime --deep --sign "${{ secrets.MACOS_CODESIGN_ID }}" "${{ env.MACOS_APP_RELEASE_PATH }}/AppFlowy.app" -v

      - name: Create macOS dmg
        run: |
          brew install create-dmg
          create-dmg \
          --volname ${{ env.MACOS_DMG_NAME }} \
          --hide-extension "AppFlowy.app" \
          --background frontend/scripts/dmg_assets/AppFlowyInstallerBackground.jpg \
          --window-size 600 450 \
          --icon-size 94 \
          --icon "AppFlowy.app" 141 249 \
          --app-drop-link 458 249 \
          "${{ env.MACOS_APP_RELEASE_PATH }}/${{ env.MACOS_DMG_NAME }}.dmg" \
          "${{ env.MACOS_APP_RELEASE_PATH }}/AppFlowy.app"

      - name: Notarize AppFlowy
        run: |
          xcrun notarytool submit ${{ env.MACOS_APP_RELEASE_PATH }}/${{ env.MACOS_DMG_NAME }}.dmg --apple-id ${{ secrets.MACOS_NOTARY_USER }} --team-id ${{ secrets.MACOS_TEAM_ID }} --password ${{ secrets.MACOS_NOTARY_PWD }} -v -f "json" --wait

      - name: Archive Asset
        working-directory: ${{ env.MACOS_APP_RELEASE_PATH }}
        run: zip --symlinks -qr ${{ env.MACOS_AARCH64_ZIP_NAME }} AppFlowy.app

      - name: Upload Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ${{ env.MACOS_APP_RELEASE_PATH }}/${{ env.MACOS_AARCH64_ZIP_NAME }}
          asset_name: ${{ env.MACOS_AARCH64_ZIP_NAME }}
          asset_content_type: application/octet-stream

      - name: Upload DMG Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ${{ env.MACOS_APP_RELEASE_PATH }}/${{ env.MACOS_DMG_NAME }}.dmg
          asset_name: ${{ env.MACOS_DMG_NAME }}.dmg
          asset_content_type: application/octet-stream

  build-for-linux:
    name: ${{ matrix.job.target }} (${{ matrix.job.os }}) [${{ matrix.job.extra-build-args }}]
    runs-on: ${{ matrix.job.os }}
    needs: create-release
    env:
      LINUX_APP_RELEASE_PATH: frontend/appflowy_flutter/product/${{ github.ref_name }}/linux/Release
      LINUX_ZIP_NAME: AppFlowy-${{ matrix.job.target }}-x86_64.tar.gz
      LINUX_PACKAGE_DEB_NAME: AppFlowy-${{ github.ref_name }}-linux-x86_64.deb
      LINUX_PACKAGE_RPM_NAME: AppFlowy-${{ github.ref_name }}-linux-x86_64.rpm
      LINUX_PACKAGE_TMP_RPM_NAME: AppFlowy-${{ github.ref_name }}-2.x86_64.rpm
      LINUX_PACKAGE_TMP_APPIMAGE_NAME: AppFlowy-${{ github.ref_name }}-x86_64.AppImage
      LINUX_PACKAGE_APPIMAGE_NAME: AppFlowy-${{ github.ref_name }}-linux-x86_64.AppImage
      LINUX_PACKAGE_ZIP_NAME: AppFlowy-${{ github.ref_name }}-linux-x86_64.tar.gz

    strategy:
      fail-fast: false
      matrix:
        job:
          - {
            arch: x86_64,
            target: x86_64-unknown-linux-gnu,
            os: ubuntu-22.04,
            extra-build-args: "",
            flutter_profile: production-linux-x86_64,
          }
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Install flutter
        uses: subosito/flutter-action@v2
        with:
          channel: "stable"
          flutter-version: ${{ env.FLUTTER_VERSION }}

      - name: Install Rust toolchain
        uses: actions-rs/toolchain@v1
        with:
          toolchain: ${{ env.RUST_TOOLCHAIN }}
          target: ${{ matrix.job.target }}
          override: true
          components: rustfmt
          profile: minimal

      - name: Install prerequisites
        working-directory: frontend
        run: |
          sudo wget -qO /etc/apt/trusted.gpg.d/dart_linux_signing_key.asc https://dl-ssl.google.com/linux/linux_signing_key.pub
          sudo apt-get update
          sudo apt-get install -y build-essential libsqlite3-dev libssl-dev clang cmake ninja-build pkg-config libgtk-3-dev libcurl4-openssl-dev
          sudo apt-get install keybinder-3.0
          sudo apt-get install -y alien libnotify-dev
          sudo apt install libfuse2
          source $HOME/.cargo/env
          cargo install --force --locked cargo-make
          cargo install --force --locked duckscript_cli
          rustup target add ${{ matrix.job.target }}

      - name: Install gcc-aarch64-linux-gnu
        if: ${{ matrix.job.target == 'aarch64-unknown-linux-gnu' }}
        working-directory: frontend
        run: |
          sudo apt-get install -qy binutils-aarch64-linux-gnu gcc-aarch64-linux-gnu g++-aarch64-linux-gnu libgtk-3-0

      - name: Build AppFlowy
        working-directory: frontend
        run: |
          flutter config --enable-linux-desktop
          dart ./scripts/flutter_release_build/build_flowy.dart run . ${{ github.ref_name }}

      - name: Archive Asset
        working-directory: ${{ env.LINUX_APP_RELEASE_PATH }}
        run: tar -czf ${{ env.LINUX_ZIP_NAME }} *

      - name: Build Linux package (.deb)
        working-directory: frontend
        run: |
          sh scripts/linux_distribution/deb/build_deb.sh appflowy_flutter/product/${{ github.ref_name }}/linux/Release ${{ github.ref_name }} ${{ env.LINUX_PACKAGE_DEB_NAME }}

      - name: Build Linux package (.rpm)
        working-directory: ${{ env.LINUX_APP_RELEASE_PATH }}
        run: |
          sudo alien -r ${{ env.LINUX_PACKAGE_DEB_NAME }}
          cp -r ${{ env.LINUX_PACKAGE_TMP_RPM_NAME }} ${{ env.LINUX_PACKAGE_RPM_NAME }}

      - name: Build Linux package (.AppImage)
        working-directory: frontend
        continue-on-error: true
        run: |
          sh scripts/linux_distribution/appimage/build_appimage.sh ${{ github.ref_name }}
          cd ..
          cp -r frontend/${{ env.LINUX_PACKAGE_TMP_APPIMAGE_NAME }} ${{ env.LINUX_APP_RELEASE_PATH }}/${{ env.LINUX_PACKAGE_APPIMAGE_NAME }}

      - name: Upload Asset
        id: upload-release-asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ${{ env.LINUX_APP_RELEASE_PATH }}/${{ env.LINUX_ZIP_NAME }}
          asset_name: ${{ env.LINUX_PACKAGE_ZIP_NAME }}
          asset_content_type: application/octet-stream

      - name: Upload Debian package
        id: upload-release-asset-install-package-deb
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ${{ env.LINUX_APP_RELEASE_PATH }}/${{ env.LINUX_PACKAGE_DEB_NAME }}
          asset_name: ${{ env.LINUX_PACKAGE_DEB_NAME }}
          asset_content_type: application/octet-stream

      - name: Upload RPM package
        id: upload-release-asset-install-package-rpm
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ${{ env.LINUX_APP_RELEASE_PATH }}/${{ env.LINUX_PACKAGE_RPM_NAME }}
          asset_name: ${{ env.LINUX_PACKAGE_RPM_NAME }}
          asset_content_type: application/octet-stream

      - name: Upload AppImage package
        id: upload-release-asset-install-package-appimage
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.create-release.outputs.upload_url }}
          asset_path: ${{ env.LINUX_APP_RELEASE_PATH }}/${{ env.LINUX_PACKAGE_APPIMAGE_NAME }}
          asset_name: ${{ env.LINUX_PACKAGE_APPIMAGE_NAME }}
          asset_content_type: application/octet-stream

  build-for-docker:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_ACCESS_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./frontend/scripts/docker-buildfiles/Dockerfile
          builder: ${{ steps.buildx.outputs.name }}
          push: true
          tags: ${{ secrets.DOCKER_HUB_USERNAME }}/appflowy_client:${{ github.ref_name }}
          cache-from: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/af_build_cache:buildcache
          cache-to: type=registry,ref=${{ secrets.DOCKER_HUB_USERNAME }}/af_build_cache:buildcache,mode=max

  notify-failure:
    runs-on: ubuntu-latest
    needs:
      - build-for-macOS-x86_64
      - build-for-windows
      - build-for-linux
    if: failure()
    steps:
      - uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          text: |
            🔴🔴🔴Workflow ${{ github.workflow }} in repository ${{ github.repository }} was failed 🔴🔴🔴.
          fields: repo,message,author,eventName,ref,workflow
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.RELEASE_SLACK_WEBHOOK }}
        if: always()

  notify-discord:
    runs-on: ubuntu-latest
    needs:
      [
        build-for-linux,
        build-for-windows,
        build-for-macOS-x86_64,
        build-for-macOS-universal,
      ]
    steps:
      - name: Notify Discord
        run: |
          curl -H "Content-Type: application/json" -d '{"username": "release@appflowy", "content": "🎉 AppFlowy ${{ github.ref_name }} is available. https://github.com/AppFlowy-IO/AppFlowy/releases/tag/'${{ github.ref_name }}'"}' "https://discord.com/api/webhooks/${{ secrets.DISCORD }}"
        shell: bash
