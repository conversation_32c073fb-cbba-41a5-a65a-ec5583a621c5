name: build

on:
  repository_dispatch:
    types: [build-command]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: notify appflowy_builder
        run: |
          platform=${{ github.event.client_payload.slash_command.args.unnamed.arg1 }}
          build_name=${{ github.event.client_payload.slash_command.args.named.build_name }}
          branch=${{ github.event.client_payload.slash_command.args.named.ref }}
          build_type=""
          arch=""

          if [ "$platform" = "android" ]; then
            build_type="apk"
          elif [ "$platform" = "macos" ]; then
            arch="universal"
          fi

          params=$(jq -n \
            --arg ref "main" \
            --arg repo "LucasXu0/AppFlowy" \
            --arg branch "$branch" \
            --arg build_name "$build_name" \
            --arg build_type "$build_type" \
            --arg arch "$arch" \
            '{ref: $ref, inputs: {repo: $repo, branch: $branch, build_name: $build_name, build_type: $build_type, arch: $arch}} | del(.inputs | .. | select(. == ""))')

          echo "params: $params"

          curl -L \
            -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer ${{ secrets.TOKEN }}" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            https://api.github.com/repos/AppFlowy-IO/AppFlowy-Builder/actions/workflows/$platform.yaml/dispatches \
            -d "$params"
