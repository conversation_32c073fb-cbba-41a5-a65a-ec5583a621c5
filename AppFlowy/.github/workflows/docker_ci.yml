name: Docker-CI

on:
  push:
    branches: [ "main", "release/*" ]
  pull_request:
    branches: [ "main", "release/*" ]
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build-app:
    if: github.event.pull_request.draft != true
    runs-on: ubuntu-latest
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      # cache the docker layers
      # don't cache anything temporarly, because it always triggers "no space left on device" error
      # - name: Cache Docker layers
      #   uses: actions/cache@v3
      #   with:
      #     path: /tmp/.buildx-cache
      #     key: ${{ runner.os }}-buildx-${{ github.sha }}
      #     restore-keys: |
      #       ${{ runner.os }}-buildx-

      - name: Build the app
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./frontend/scripts/docker-buildfiles/Dockerfile
          push: false
          # cache-from: type=local,src=/tmp/.buildx-cache
          # cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max

      # - name: Move cache
      #   run: |
      #     rm -rf /tmp/.buildx-cache
      #     mv /tmp/.buildx-cache-new /tmp/.buildx-cache
