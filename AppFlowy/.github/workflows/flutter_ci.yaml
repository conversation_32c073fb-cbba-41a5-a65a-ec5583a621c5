name: Flutter-CI

on:
  push:
    branches:
      - "main"
      - "release/*"
    paths:
      - ".github/workflows/flutter_ci.yaml"
      - ".github/actions/flutter_build/**"
      - "frontend/rust-lib/**"
      - "frontend/appflowy_flutter/**"
      - "frontend/resources/**"

  pull_request:
    branches:
      - "main"
      - "release/*"
    paths:
      - ".github/workflows/flutter_ci.yaml"
      - ".github/actions/flutter_build/**"
      - "frontend/rust-lib/**"
      - "frontend/appflowy_flutter/**"
      - "frontend/resources/**"

env:
  CARGO_TERM_COLOR: always
  FLUTTER_VERSION: "3.27.4"
  RUST_TOOLCHAIN: "1.85.0"
  CARGO_MAKE_VERSION: "0.37.18"
  CLOUD_VERSION: 0.9.49-amd64

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  prepare-linux:
    if: github.event.pull_request.draft != true
    strategy:
      fail-fast: true
      matrix:
        os: [ ubuntu-latest ]
        include:
          - os: ubuntu-latest
            flutter_profile: development-linux-x86_64
            target: x86_64-unknown-linux-gnu
    runs-on: ${{ matrix.os }}

    steps:
      # the following step is required to avoid running out of space
      - name: Maximize build space
        run: |
          sudo rm -rf /usr/share/dotnet
          sudo rm -rf /opt/ghc
          sudo rm -rf "/usr/local/share/boost"
          sudo rm -rf "$AGENT_TOOLSDIRECTORY"

      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Flutter build
        uses: ./.github/actions/flutter_build
        with:
          os: ${{ matrix.os }}
          flutter_version: ${{ env.FLUTTER_VERSION }}
          rust_toolchain: ${{ env.RUST_TOOLCHAIN }}
          cargo_make_version: ${{ env.CARGO_MAKE_VERSION }}
          rust_target: ${{ matrix.target }}
          flutter_profile: ${{ matrix.flutter_profile }}

  prepare-windows:
    if: github.event.pull_request.draft != true
    strategy:
      fail-fast: true
      matrix:
        os: [ windows-latest ]
        include:
          - os: windows-latest
            flutter_profile: development-windows-x86
            target: x86_64-pc-windows-msvc
    runs-on: ${{ matrix.os }}

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Flutter build
        uses: ./.github/actions/flutter_build
        with:
          os: ${{ matrix.os }}
          flutter_version: ${{ env.FLUTTER_VERSION }}
          DISABLE_CI_TEST_LOG: "true"
          rust_toolchain: ${{ env.RUST_TOOLCHAIN }}
          cargo_make_version: ${{ env.CARGO_MAKE_VERSION }}
          rust_target: ${{ matrix.target }}
          flutter_profile: ${{ matrix.flutter_profile }}

  prepare-macos:
    if: github.event.pull_request.draft != true
    strategy:
      fail-fast: true
      matrix:
        os: [ macos-latest ]
        include:
          - os: macos-latest
            flutter_profile: development-mac-x86_64
            target: x86_64-apple-darwin
    runs-on: ${{ matrix.os }}

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Flutter build
        uses: ./.github/actions/flutter_build
        with:
          os: ${{ matrix.os }}
          flutter_version: ${{ env.FLUTTER_VERSION }}
          rust_toolchain: ${{ env.RUST_TOOLCHAIN }}
          cargo_make_version: ${{ env.CARGO_MAKE_VERSION }}
          rust_target: ${{ matrix.target }}
          flutter_profile: ${{ matrix.flutter_profile }}

  unit_test:
    needs: [ prepare-linux ]
    if: github.event.pull_request.draft != true
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest ]
        include:
          - os: ubuntu-latest
            flutter_profile: development-linux-x86_64
            target: x86_64-unknown-linux-gnu
    runs-on: ${{ matrix.os }}

    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Install Rust toolchain
        id: rust_toolchain
        uses: actions-rs/toolchain@v1
        with:
          toolchain: ${{ env.RUST_TOOLCHAIN }}
          target: ${{ matrix.target }}
          override: true
          profile: minimal

      - name: Install flutter
        id: flutter
        uses: subosito/flutter-action@v2
        with:
          channel: "stable"
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true

      - uses: Swatinem/rust-cache@v2
        with:
          prefix-key: ${{ matrix.os }}
          workspaces: |
            frontend/rust-lib
          cache-all-crates: true

      - uses: taiki-e/install-action@v2
        with:
          tool: cargo-make@${{ env.CARGO_MAKE_VERSION }}, duckscript_cli

      - name: Install prerequisites
        working-directory: frontend
        run: |
          if [ "$RUNNER_OS" == "Linux" ]; then
            sudo wget -qO /etc/apt/trusted.gpg.d/dart_linux_signing_key.asc https://dl-ssl.google.com/linux/linux_signing_key.pub
            sudo wget -qO /etc/apt/sources.list.d/dart_stable.list https://storage.googleapis.com/download.dartlang.org/linux/debian/dart_stable.list
            sudo apt-get update
            sudo apt-get install -y dart curl build-essential libssl-dev clang cmake ninja-build pkg-config libgtk-3-dev keybinder-3.0 libnotify-dev libcurl4-openssl-dev
          fi
        shell: bash

      - name: Enable Flutter Desktop
        run: |
          if [ "$RUNNER_OS" == "Linux" ]; then
            flutter config --enable-linux-desktop
          elif [ "$RUNNER_OS" == "macOS" ]; then
            flutter config --enable-macos-desktop
          elif [ "$RUNNER_OS" == "Windows" ]; then
            git config --system core.longpaths true
            flutter config --enable-windows-desktop
          fi
        shell: bash

      - uses: actions/download-artifact@v4
        with:
          name: ${{ github.run_id }}-${{ matrix.os }}

      - name: Uncompress appflowy_flutter
        run: tar -xf appflowy_flutter.tar.gz

      - name: Run flutter pub get
        working-directory: frontend
        run: cargo make pub_get

      - name: Run Flutter unit tests
        env:
          DISABLE_EVENT_LOG: true
          DISABLE_CI_TEST_LOG: "true"
        working-directory: frontend
        run: |
          if [ "$RUNNER_OS" == "macOS" ]; then
            cargo make dart_unit_test
          elif [ "$RUNNER_OS" == "Linux" ]; then
            cargo make dart_unit_test_no_build
          elif [ "$RUNNER_OS" == "Windows" ]; then
            cargo make dart_unit_test_no_build
          fi
        shell: bash

  cloud_integration_test:
    needs: [ prepare-linux ]
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest ]
        include:
          - os: ubuntu-latest
            flutter_profile: development-linux-x86_64
            target: x86_64-unknown-linux-gnu
    runs-on: ${{ matrix.os }}

    steps:
      - name: Checkout appflowy cloud code
        uses: actions/checkout@v4
        with:
          repository: AppFlowy-IO/AppFlowy-Cloud
          path: AppFlowy-Cloud

      - name: Prepare appflowy cloud env
        working-directory: AppFlowy-Cloud
        run: |
          # log level
          cp deploy.env .env
          sed -i 's|RUST_LOG=.*|RUST_LOG=trace|' .env
          sed -i 's/GOTRUE_EXTERNAL_GOOGLE_ENABLED=.*/GOTRUE_EXTERNAL_GOOGLE_ENABLED=true/' .env
          sed -i 's|GOTRUE_MAILER_AUTOCONFIRM=.*|GOTRUE_MAILER_AUTOCONFIRM=true|' .env
          sed -i 's|API_EXTERNAL_URL=.*|API_EXTERNAL_URL=http://localhost|' .env

      - name: Run Docker-Compose
        working-directory: AppFlowy-Cloud
        env:
          APPFLOWY_CLOUD_VERSION: ${{ env.CLOUD_VERSION }}
          APPFLOWY_HISTORY_VERSION: ${{ env.CLOUD_VERSION }}
          APPFLOWY_WORKER_VERSION: ${{ env.CLOUD_VERSION }}
        run: |
          container_id=$(docker ps --filter name=appflowy-cloud-appflowy_cloud-1 -q)
          if [ -z "$container_id" ]; then
            echo "AppFlowy-Cloud container is not running. Pulling and starting the container..."
            docker compose pull
            docker compose up -d
            echo "Waiting for the container to be ready..."
            sleep 10
          else
            running_image=$(docker inspect --format='{{index .Config.Image}}' "$container_id")
            if [ "$running_image" != "appflowy-cloud:$APPFLOWY_CLOUD_VERSION" ]; then
              echo "AppFlowy-Cloud is running with an incorrect version. Restarting with the correct version..."
              # Remove all containers if any exist
              if [ "$(docker ps -aq)" ]; then
                docker rm -f $(docker ps -aq)
              else
                echo "No containers to remove."
              fi

              # Remove all volumes if any exist
              if [ "$(docker volume ls -q)" ]; then
                docker volume rm $(docker volume ls -q)
              else
                echo "No volumes to remove."
              fi
              docker compose pull
              docker compose up -d
              echo "Waiting for the container to be ready..."
              sleep 10
              docker ps -a
              docker compose logs
            else
              echo "AppFlowy-Cloud is running with the correct version."
            fi
          fi

      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Install flutter
        id: flutter
        uses: subosito/flutter-action@v2
        with:
          channel: "stable"
          flutter-version: ${{ env.FLUTTER_VERSION }}
          cache: true

      - uses: taiki-e/install-action@v2
        with:
          tool: cargo-make@${{ env.CARGO_MAKE_VERSION }}

      - name: Install prerequisites
        working-directory: frontend
        run: |
          sudo wget -qO /etc/apt/trusted.gpg.d/dart_linux_signing_key.asc https://dl-ssl.google.com/linux/linux_signing_key.pub
          sudo wget -qO /etc/apt/sources.list.d/dart_stable.list https://storage.googleapis.com/download.dartlang.org/linux/debian/dart_stable.list
          sudo apt-get update
          sudo apt-get install -y dart curl build-essential libssl-dev clang cmake ninja-build pkg-config libgtk-3-dev keybinder-3.0 libnotify-dev libcurl4-openssl-dev
        shell: bash

      - name: Enable Flutter Desktop
        run: |
          flutter config --enable-linux-desktop
        shell: bash

      - uses: actions/download-artifact@v4
        with:
          name: ${{ github.run_id }}-${{ matrix.os }}

      - name: Uncompressed appflowy_flutter
        run: |
          tar -xf appflowy_flutter.tar.gz
          ls -al

      - name: Run flutter pub get
        working-directory: frontend
        run: cargo make pub_get

      - name: Run Flutter integration tests
        working-directory: frontend/appflowy_flutter
        run: |
          export DISPLAY=:99
          sudo Xvfb -ac :99 -screen 0 1280x1024x24 > /dev/null 2>&1 &
          sudo apt-get install network-manager
          docker ps -a
          flutter test integration_test/desktop/cloud/cloud_runner.dart -d Linux --coverage
        shell: bash

  integration_test:
    needs: [ prepare-linux ]
    if: github.event.pull_request.draft != true
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest ]
        test_number: [ 1, 2, 3, 4, 5, 6, 7, 8, 9 ]
        include:
          - os: ubuntu-latest
            target: "x86_64-unknown-linux-gnu"
    runs-on: ${{ matrix.os }}
    steps:
      - name: Checkout source code
        uses: actions/checkout@v4

      - name: Flutter Integration Test ${{ matrix.test_number }}
        uses: ./.github/actions/flutter_integration_test
        with:
          test_path: integration_test/desktop_runner_${{ matrix.test_number }}.dart
          flutter_version: ${{ env.FLUTTER_VERSION }}
          rust_toolchain: ${{ env.RUST_TOOLCHAIN }}
          cargo_make_version: ${{ env.CARGO_MAKE_VERSION }}
          rust_target: ${{ matrix.target }}
