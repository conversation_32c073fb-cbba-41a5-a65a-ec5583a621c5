import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar as CalendarIcon, Download } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { ExportOptions } from '@/lib/exportImportEngine';

interface ExportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onExport: (options: ExportOptions) => void;
  isExporting: boolean;
}

export function ExportDialog({
  open,
  onOpenChange,
  onExport,
  isExporting
}: ExportDialogProps) {
  const [includeSettings, setIncludeSettings] = useState(true);
  const [includeAgents, setIncludeAgents] = useState(true);
  const [includeTasks, setIncludeTasks] = useState(true);
  const [includeMemory, setIncludeMemory] = useState(true);
  const [includeMetrics, setIncludeMetrics] = useState(true);
  const [format, setFormat] = useState<'json' | 'csv' | 'yaml' | 'zip'>('json');
  const [useDateRange, setUseDateRange] = useState(false);
  const [dateRange, setDateRange] = useState<{
    start: Date | undefined;
    end: Date | undefined;
  }>({
    start: undefined,
    end: undefined
  });

  const handleExport = () => {
    const options: ExportOptions = {
      includeSettings,
      includeAgents,
      includeTasks,
      includeMemory,
      includeMetrics,
      format,
      dateRange: useDateRange && dateRange.start && dateRange.end
        ? { start: dateRange.start, end: dateRange.end }
        : undefined
    };
    onExport(options);
  };

  const isExportDisabled = !includeSettings && !includeAgents && !includeTasks && !includeMemory && !includeMetrics;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Export Claude Flow Data</DialogTitle>
          <DialogDescription>
            Choose what data to export and in which format.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* Data Selection */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Select Data to Export</Label>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="settings"
                  checked={includeSettings}
                  onCheckedChange={setIncludeSettings}
                />
                <Label
                  htmlFor="settings"
                  className="text-sm font-normal cursor-pointer"
                >
                  Settings & Configuration
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="agents"
                  checked={includeAgents}
                  onCheckedChange={setIncludeAgents}
                />
                <Label
                  htmlFor="agents"
                  className="text-sm font-normal cursor-pointer"
                >
                  Agents & Templates
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="tasks"
                  checked={includeTasks}
                  onCheckedChange={setIncludeTasks}
                />
                <Label
                  htmlFor="tasks"
                  className="text-sm font-normal cursor-pointer"
                >
                  Tasks & History
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="memory"
                  checked={includeMemory}
                  onCheckedChange={setIncludeMemory}
                />
                <Label
                  htmlFor="memory"
                  className="text-sm font-normal cursor-pointer"
                >
                  Memory Snapshots
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="metrics"
                  checked={includeMetrics}
                  onCheckedChange={setIncludeMetrics}
                />
                <Label
                  htmlFor="metrics"
                  className="text-sm font-normal cursor-pointer"
                >
                  Metrics & Performance Data
                </Label>
              </div>
            </div>
          </div>

          {/* Export Format */}
          <div className="space-y-2">
            <Label htmlFor="format">Export Format</Label>
            <Select value={format} onValueChange={(value: any) => setFormat(value)}>
              <SelectTrigger id="format">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="json">JSON (Structured Data)</SelectItem>
                <SelectItem value="csv">CSV (Tabular Data)</SelectItem>
                <SelectItem value="yaml">YAML (Human Readable)</SelectItem>
                <SelectItem value="zip">ZIP (Complete Backup)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Date Range Filter */}
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="date-range"
                checked={useDateRange}
                onCheckedChange={setUseDateRange}
              />
              <Label
                htmlFor="date-range"
                className="text-sm font-normal cursor-pointer"
              >
                Filter by date range
              </Label>
            </div>

            {useDateRange && (
              <div className="flex items-center gap-2 pl-6">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-[140px] justify-start text-left font-normal",
                        !dateRange.start && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange.start ? format(dateRange.start, "PPP") : "Start date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateRange.start}
                      onSelect={(date) => setDateRange(prev => ({ ...prev, start: date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>

                <span className="text-sm text-muted-foreground">to</span>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-[140px] justify-start text-left font-normal",
                        !dateRange.end && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange.end ? format(dateRange.end, "PPP") : "End date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={dateRange.end}
                      onSelect={(date) => setDateRange(prev => ({ ...prev, end: date }))}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isExporting}
          >
            Cancel
          </Button>
          <Button
            onClick={handleExport}
            disabled={isExportDisabled || isExporting}
            className="gap-2"
          >
            <Download className="h-4 w-4" />
            {isExporting ? 'Exporting...' : 'Export'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}