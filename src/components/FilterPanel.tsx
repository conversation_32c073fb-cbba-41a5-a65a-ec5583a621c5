import React from 'react';
import { Filter, X, ChevronDown, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { SearchFilters, FilterOption, SavedFilter } from '@/types/claudeFlowSearch';

interface FilterPanelProps {
  filters: SearchFilters;
  onFiltersChange: (filters: Partial<SearchFilters>) => void;
  onClearFilters: () => void;
  filterOptions: {
    status: FilterOption[];
    type: FilterOption[];
    agents: FilterOption[];
  };
  savedFilters: SavedFilter[];
  onLoadSavedFilter: (filterId: string) => void;
  onDeleteSavedFilter: (filterId: string) => void;
  className?: string;
}

export function FilterPanel({
  filters,
  onFiltersChange,
  onClearFilters,
  filterOptions,
  savedFilters,
  onLoadSavedFilter,
  onDeleteSavedFilter,
  className,
}: FilterPanelProps) {
  const activeFilterCount = [
    filters.status?.length,
    filters.type?.length,
    filters.priority,
    filters.dateRange,
    filters.assignedAgent,
    filters.tags?.length,
  ].filter(Boolean).length;

  const handleStatusChange = (value: string, checked: boolean) => {
    const currentStatus = filters.status || [];
    const newStatus = checked
      ? [...currentStatus, value]
      : currentStatus.filter(s => s !== value);
    onFiltersChange({ status: newStatus.length > 0 ? newStatus : undefined });
  };

  const handleTypeChange = (value: string, checked: boolean) => {
    const currentType = filters.type || [];
    const newType = checked
      ? [...currentType, value]
      : currentType.filter(t => t !== value);
    onFiltersChange({ type: newType.length > 0 ? newType : undefined });
  };

  const handlePriorityChange = (value: number[]) => {
    onFiltersChange({
      priority: { min: value[0], max: value[1] },
    });
  };

  const handleDateRangeChange = (type: 'start' | 'end', date: Date | undefined) => {
    const currentRange = filters.dateRange || {};
    onFiltersChange({
      dateRange: {
        ...currentRange,
        [type]: date,
      },
    });
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <h3 className="font-medium">Filters</h3>
          {activeFilterCount > 0 && (
            <Badge variant="secondary" className="ml-2">
              {activeFilterCount} active
            </Badge>
          )}
        </div>
        {activeFilterCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="h-7 text-xs"
          >
            Clear all
          </Button>
        )}
      </div>

      {/* Saved Filters */}
      {savedFilters.length > 0 && (
        <div className="space-y-2">
          <Label className="text-sm">Saved Filters</Label>
          <div className="space-y-1">
            {savedFilters.map(filter => (
              <div
                key={filter.id}
                className="flex items-center justify-between p-2 rounded-md hover:bg-accent group"
              >
                <button
                  onClick={() => onLoadSavedFilter(filter.id)}
                  className="flex-1 text-left text-sm"
                >
                  {filter.name}
                </button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDeleteSavedFilter(filter.id)}
                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Entity Type */}
      <div className="space-y-2">
        <Label htmlFor="entity-type" className="text-sm">Search in</Label>
        <Select
          value={filters.entityType}
          onValueChange={(value) => onFiltersChange({ entityType: value as any })}
        >
          <SelectTrigger id="entity-type">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="agents">Agents</SelectItem>
            <SelectItem value="tasks">Tasks</SelectItem>
            <SelectItem value="memories">Memories</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Status Filter */}
      <Collapsible defaultOpen>
        <CollapsibleTrigger className="flex items-center justify-between w-full py-2">
          <Label className="text-sm cursor-pointer">Status</Label>
          <ChevronDown className="h-4 w-4" />
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-2 pt-2">
          {filterOptions.status.map(option => (
            <div key={option.value} className="flex items-center space-x-2">
              <Checkbox
                id={`status-${option.value}`}
                checked={filters.status?.includes(option.value) || false}
                onCheckedChange={(checked) =>
                  handleStatusChange(option.value, checked as boolean)
                }
              />
              <Label
                htmlFor={`status-${option.value}`}
                className="text-sm font-normal cursor-pointer flex-1"
              >
                {option.label}
                {option.count !== undefined && (
                  <span className="text-muted-foreground ml-1">({option.count})</span>
                )}
              </Label>
            </div>
          ))}
        </CollapsibleContent>
      </Collapsible>

      {/* Type Filter */}
      <Collapsible defaultOpen>
        <CollapsibleTrigger className="flex items-center justify-between w-full py-2">
          <Label className="text-sm cursor-pointer">Type</Label>
          <ChevronDown className="h-4 w-4" />
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-2 pt-2">
          {filterOptions.type.map(option => (
            <div key={option.value} className="flex items-center space-x-2">
              <Checkbox
                id={`type-${option.value}`}
                checked={filters.type?.includes(option.value) || false}
                onCheckedChange={(checked) =>
                  handleTypeChange(option.value, checked as boolean)
                }
              />
              <Label
                htmlFor={`type-${option.value}`}
                className="text-sm font-normal cursor-pointer flex-1"
              >
                {option.label}
                {option.count !== undefined && (
                  <span className="text-muted-foreground ml-1">({option.count})</span>
                )}
              </Label>
            </div>
          ))}
        </CollapsibleContent>
      </Collapsible>

      {/* Priority Filter */}
      <Collapsible>
        <CollapsibleTrigger className="flex items-center justify-between w-full py-2">
          <Label className="text-sm cursor-pointer">Priority</Label>
          <ChevronDown className="h-4 w-4" />
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-2 pt-2">
          <div className="px-1">
            <Slider
              value={[filters.priority?.min || 1, filters.priority?.max || 10]}
              onValueChange={handlePriorityChange}
              min={1}
              max={10}
              step={1}
              className="w-full"
            />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>{filters.priority?.min || 1}</span>
              <span>{filters.priority?.max || 10}</span>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Date Range Filter */}
      <Collapsible>
        <CollapsibleTrigger className="flex items-center justify-between w-full py-2">
          <Label className="text-sm cursor-pointer">Date Range</Label>
          <ChevronDown className="h-4 w-4" />
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-2 pt-2">
          <div className="space-y-2">
            <div>
              <Label className="text-xs text-muted-foreground">From</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal h-8"
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    {filters.dateRange?.start
                      ? format(filters.dateRange.start, 'PP')
                      : 'Select date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    mode="single"
                    selected={filters.dateRange?.start}
                    onSelect={(date) => handleDateRangeChange('start', date)}
                  />
                </PopoverContent>
              </Popover>
            </div>
            <div>
              <Label className="text-xs text-muted-foreground">To</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full justify-start text-left font-normal h-8"
                  >
                    <Calendar className="mr-2 h-4 w-4" />
                    {filters.dateRange?.end
                      ? format(filters.dateRange.end, 'PP')
                      : 'Select date'}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <CalendarComponent
                    mode="single"
                    selected={filters.dateRange?.end}
                    onSelect={(date) => handleDateRangeChange('end', date)}
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Assigned Agent Filter (for tasks) */}
      {filters.entityType === 'tasks' && filterOptions.agents.length > 0 && (
        <div className="space-y-2">
          <Label htmlFor="assigned-agent" className="text-sm">Assigned Agent</Label>
          <Select
            value={filters.assignedAgent || ''}
            onValueChange={(value) =>
              onFiltersChange({ assignedAgent: value || undefined })
            }
          >
            <SelectTrigger id="assigned-agent">
              <SelectValue placeholder="Any agent" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Any agent</SelectItem>
              {filterOptions.agents.map(agent => (
                <SelectItem key={agent.value} value={agent.value}>
                  {agent.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
    </div>
  );
}