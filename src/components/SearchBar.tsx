import React, { useState, useRef, useEffect } from 'react';
import { Search, X, Clock, Save } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  onSearch: (value: string) => void;
  onClear: () => void;
  onSaveFilter?: () => void;
  searchHistory: string[];
  isSearching: boolean;
  placeholder?: string;
  className?: string;
  autoFocus?: boolean;
}

export function SearchBar({
  value,
  onChange,
  onSearch,
  onClear,
  onSaveFilter,
  searchHistory,
  isSearching,
  placeholder = 'Search agents, tasks, memories...',
  className,
  autoFocus = false,
}: SearchBarProps) {
  const [isHistoryOpen, setIsHistoryOpen] = useState(false);
  const [isSaveDialogOpen, setIsSaveDialogOpen] = useState(false);
  const [filterName, setFilterName] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(value);
  };

  const handleHistorySelect = (query: string) => {
    onChange(query);
    onSearch(query);
    setIsHistoryOpen(false);
  };

  const handleSaveFilter = () => {
    if (onSaveFilter && filterName.trim()) {
      onSaveFilter();
      setIsSaveDialogOpen(false);
      setFilterName('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Escape') {
      onClear();
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit} className={cn('relative', className)}>
        <div className="relative flex items-center">
          <Search className="absolute left-3 h-4 w-4 text-muted-foreground pointer-events-none" />
          <Input
            ref={inputRef}
            type="text"
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            className="pl-10 pr-20"
            disabled={isSearching}
          />
          <div className="absolute right-2 flex items-center gap-1">
            {value && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={onClear}
                className="h-7 w-7 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
            
            {searchHistory.length > 0 && (
              <Popover open={isHistoryOpen} onOpenChange={setIsHistoryOpen}>
                <PopoverTrigger asChild>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-7 w-7 p-0"
                  >
                    <Clock className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-2" align="end">
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-muted-foreground px-2 py-1">
                      Recent searches
                    </div>
                    {searchHistory.slice(0, 10).map((query, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => handleHistorySelect(query)}
                        className="w-full text-left px-2 py-1.5 text-sm rounded hover:bg-accent hover:text-accent-foreground transition-colors"
                      >
                        {query}
                      </button>
                    ))}
                  </div>
                </PopoverContent>
              </Popover>
            )}

            {onSaveFilter && value && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => setIsSaveDialogOpen(true)}
                className="h-7 w-7 p-0"
                title="Save current search"
              >
                <Save className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </form>

      <Dialog open={isSaveDialogOpen} onOpenChange={setIsSaveDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Save Search Filter</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="filter-name">Filter Name</Label>
              <Input
                id="filter-name"
                value={filterName}
                onChange={(e) => setFilterName(e.target.value)}
                placeholder="e.g., Failed Tasks Today"
                autoFocus
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsSaveDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveFilter} disabled={!filterName.trim()}>
              Save Filter
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}