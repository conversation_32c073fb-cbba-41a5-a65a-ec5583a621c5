import React, { use<PERSON>emo, use<PERSON><PERSON>back, useState, useRef, useEffect } from "react";
import { motion, AnimatePresence, LayoutGroup } from "framer-motion";
import { 
  FileText, 
  ArrowLeft, 
  Calendar, 
  Clock, 
  Search,
  Filter,
  ChevronRight,
  Code2,
  Compass,
  Hash,
  MoreVertical,
  Copy,
  ExternalLink
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Pagination } from "@/components/ui/pagination";
import { Badge, StatusBadge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/components/ui/tooltip";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ClaudeMemoriesDropdown } from "@/components/ClaudeMemoriesDropdown";
import { cn } from "@/lib/utils";
import { formatUnixTimestamp, formatISOTimestamp } from "@/lib/date-utils";
import { usePagination } from "@/hooks/usePagination";
import type { Session, ClaudeMdFile, SessionMode } from "@/lib/api";

interface SessionListProps {
  sessions: Session[];
  projectPath: string;
  onBack: () => void;
  onSessionClick?: (session: Session) => void;
  onEditClaudeFile?: (file: ClaudeMdFile) => void;
  className?: string;
}

// Skeleton loading component for better perceived performance
const SessionCardSkeleton: React.FC = () => (
  <Card className="p-6">
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Skeleton className="h-5 w-5 rounded" />
          <Skeleton className="h-5 w-40" />
        </div>
        <Skeleton className="h-8 w-8 rounded" />
      </div>
      <div className="flex gap-4">
        <Skeleton className="h-4 w-24" />
        <Skeleton className="h-4 w-32" />
      </div>
      <Skeleton className="h-3 w-48" />
    </div>
  </Card>
);

// Mode badge component
const ModeBadge: React.FC<{ mode: SessionMode }> = ({ mode }) => {
  const modeConfig = {
    architect: {
      icon: Compass,
      label: "Architect",
      variant: "purple" as const,
      tooltip: "Planning-focused mode with design emphasis"
    },
    code: {
      icon: Code2,
      label: "Code",
      variant: "success" as const,
      tooltip: "Implementation-focused mode with full tool access"
    }
  };

  const config = modeConfig[mode] || modeConfig.code;
  const Icon = config.icon;

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge variant={config.variant} size="sm" className="gap-1">
            <Icon className="h-3 w-3" />
            {config.label}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p className="text-xs">{config.tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

// Enhanced session card with better visual hierarchy and interactions
const SessionCard = React.memo<{
  session: Session;
  projectPath: string;
  onClick?: () => void;
  onEditClaudeFile?: (file: ClaudeMdFile) => void;
  isSelected?: boolean;
}>(({ session, projectPath, onClick, onEditClaudeFile, isSelected }) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isCopied, setIsCopied] = useState(false);

  const formatTime = useCallback((timestamp: string | number | undefined) => {
    if (!timestamp) return "Unknown time";
    
    if (typeof timestamp === "string") {
      return formatISOTimestamp(timestamp);
    } else {
      return formatUnixTimestamp(timestamp);
    }
  }, []);

  const handleCopyId = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    navigator.clipboard.writeText(session.id);
    setIsCopied(true);
    setTimeout(() => setIsCopied(false), 2000);
  }, [session.id]);

  const sessionTitle = useMemo(() => {
    if (session.first_message) {
      // Extract first meaningful line from the message
      const firstLine = session.first_message.split('\n')[0].trim();
      return firstLine.length > 60 ? firstLine.substring(0, 60) + '...' : firstLine;
    }
    return `Session ${session.id.slice(0, 8)}`;
  }, [session]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      whileHover={{ scale: 1.01 }}
      whileTap={{ scale: 0.99 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      layout
    >
      <Card 
        className={cn(
          "cursor-pointer transition-all duration-200",
          "hover:shadow-lg hover:border-primary/30",
          "bg-card/50 backdrop-blur-sm",
          isSelected && "border-primary shadow-lg ring-2 ring-primary/20",
          isHovered && "bg-card/70"
        )}
        onClick={onClick}
      >
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Header section */}
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-3 mb-2">
                  <motion.div
                    animate={{ rotate: isHovered ? 360 : 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <FileText className="h-5 w-5 text-primary flex-shrink-0" />
                  </motion.div>
                  <h3 className="font-semibold text-lg truncate" title={sessionTitle}>
                    {sessionTitle}
                  </h3>
                  <AnimatePresence>
                    {isHovered && (
                      <motion.div
                        initial={{ opacity: 0, x: -10 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: -10 }}
                      >
                        <ChevronRight className="h-4 w-4 text-muted-foreground" />
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Mode and metadata */}
                <div className="flex flex-wrap items-center gap-3">
                  <ModeBadge mode={session.mode} />
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3.5 w-3.5" />
                            <span>{formatTime(session.created_at)}</span>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="text-xs">Session created</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    {session.message_timestamp && (
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center gap-1">
                              <Clock className="h-3.5 w-3.5" />
                              <span>{formatTime(session.message_timestamp)}</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="text-xs">Last message</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                </div>
              </div>

              {/* Actions section */}
              <div className="flex items-start gap-2">
                <ClaudeMemoriesDropdown
                  projectPath={projectPath}
                  onEditFile={onEditClaudeFile || (() => {})}
                />
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-opacity"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <MoreVertical className="h-4 w-4" />
                      <span className="sr-only">More options</span>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleCopyId}>
                      <Copy className="h-4 w-4 mr-2" />
                      {isCopied ? 'Copied!' : 'Copy Session ID'}
                    </DropdownMenuItem>
                    <DropdownMenuItem>
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open in new window
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem className="text-destructive">
                      Archive session
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {/* Session ID with copy functionality */}
            <div className="flex items-center gap-2">
              <Hash className="h-3 w-3 text-muted-foreground" />
              <code className="text-xs text-muted-foreground font-mono select-all">
                {session.id}
              </code>
              <AnimatePresence>
                {isHovered && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={handleCopyId}
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Mode transitions indicator */}
            {session.mode_history && session.mode_history.length > 0 && (
              <div className="pt-2 border-t">
                <p className="text-xs text-muted-foreground">
                  {session.mode_history.length} mode transition{session.mode_history.length !== 1 && 's'}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
});

SessionCard.displayName = 'SessionCard';

// Empty state component
const EmptyState: React.FC = () => (
  <motion.div
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    className="flex flex-col items-center justify-center h-full min-h-[400px]"
  >
    <Card className="bg-muted/20 border-dashed max-w-md w-full">
      <CardContent className="p-12 text-center space-y-4">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 200, damping: 15 }}
        >
          <FileText className="h-16 w-16 text-muted-foreground mx-auto" />
        </motion.div>
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">No sessions found</h3>
          <p className="text-sm text-muted-foreground">
            Start a new Claude session to see it appear here
          </p>
        </div>
        <Button variant="outline" className="mt-4">
          <FileText className="h-4 w-4 mr-2" />
          Create new session
        </Button>
      </CardContent>
    </Card>
  </motion.div>
);

export const SessionList: React.FC<SessionListProps> = React.memo(({
  sessions,
  projectPath,
  onBack,
  onSessionClick,
  onEditClaudeFile,
  className
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Sort and filter sessions
  const filteredAndSortedSessions = useMemo(() => {
    let filtered = sessions;
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = sessions.filter(session => 
        session.id.toLowerCase().includes(query) ||
        session.first_message?.toLowerCase().includes(query) ||
        session.mode.toLowerCase().includes(query)
      );
    }

    // Sort by created_at in descending order
    return [...filtered].sort((a, b) => {
      const timeA = a.created_at || 0;
      const timeB = b.created_at || 0;
      return timeB > timeA ? 1 : -1;
    });
  }, [sessions, searchQuery]);

  // Use custom pagination hook
  const {
    currentPage,
    totalPages,
    paginatedData,
    goToPage,
    canGoNext,
    canGoPrevious
  } = usePagination(filteredAndSortedSessions, {
    initialPage: 1,
    initialPageSize: 6
  });

  const handleSessionClick = useCallback((session: Session) => {
    setSelectedSessionId(session.id);
    onSessionClick?.(session);
  }, [onSessionClick]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === '/' && e.metaKey) {
        e.preventDefault();
        searchInputRef.current?.focus();
      }
      if (e.key === 'Escape') {
        searchInputRef.current?.blur();
        setSearchQuery("");
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Simulate loading state for demo
  useEffect(() => {
    setIsLoading(true);
    const timer = setTimeout(() => setIsLoading(false), 500);
    return () => clearTimeout(timer);
  }, [currentPage]);

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Header - Fixed */}
      <div className="flex-shrink-0 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="p-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={onBack}
                className="h-8 w-8"
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="sr-only">Go back</span>
              </Button>
              <div>
                <h2 className="text-2xl font-bold">Sessions</h2>
                <p className="text-sm text-muted-foreground">
                  {projectPath}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <StatusBadge 
                status="active" 
                showIcon 
                className="text-xs"
              >
                {sessions.length} {sessions.length === 1 ? 'session' : 'sessions'}
              </StatusBadge>
            </div>
          </div>

          {/* Search and filter bar */}
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                ref={searchInputRef}
                type="search"
                placeholder="Search sessions... (⌘/)"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 pr-4"
              />
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon">
                    <Filter className="h-4 w-4" />
                    <span className="sr-only">Filter sessions</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs">Filter options coming soon</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>

      {/* Sessions list - Scrollable */}
      <ScrollArea className="flex-1">
        <div className="p-4">
          {isLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <SessionCardSkeleton key={i} />
              ))}
            </div>
          ) : filteredAndSortedSessions.length === 0 ? (
            searchQuery ? (
              <Card className="bg-muted/20">
                <CardContent className="p-8 text-center">
                  <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    No sessions found matching "{searchQuery}"
                  </p>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="mt-4"
                    onClick={() => setSearchQuery("")}
                  >
                    Clear search
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <EmptyState />
            )
          ) : (
            <LayoutGroup>
              <AnimatePresence mode="popLayout">
                <motion.div
                  key={currentPage}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="space-y-4"
                >
                  {paginatedData.map((session) => (
                    <SessionCard
                      key={session.id}
                      session={session}
                      projectPath={projectPath}
                      onClick={() => handleSessionClick(session)}
                      onEditClaudeFile={onEditClaudeFile}
                      isSelected={selectedSessionId === session.id}
                    />
                  ))}
                </motion.div>
              </AnimatePresence>
            </LayoutGroup>
          )}
        </div>
      </ScrollArea>

      {/* Pagination - Fixed at bottom */}
      {totalPages > 1 && !isLoading && (
        <div className="flex-shrink-0 border-t border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="p-4 flex justify-center">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={goToPage}
            />
          </div>
        </div>
      )}
    </div>
  );
});

SessionList.displayName = 'SessionList';