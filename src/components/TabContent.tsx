import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useTabState } from '@/hooks/useTabState';
import { Tab } from '@/contexts/TabContext';
import { Welcome } from '@/components/Welcome';
import { useBackToWelcome } from '@/hooks/useBackToWelcome';
import { TabContentFactory } from '@/lib/tabContentFactory';
import { useEventBus } from '@/lib/eventBus.tsx';

interface TabPanelProps {
  tab: Tab;
  isActive: boolean;
}

/**
 * Simplified TabPanel component using the factory pattern
 */
const TabPanel: React.FC<TabPanelProps> = ({ tab, isActive }) => {
  const { updateTab, createChatTab, closeTab, createProjectSettingsTab } = useTabState();
  const goToWelcome = useBackToWelcome();
  
  // Panel visibility - hide when not active
  const panelVisibilityClass = isActive ? "" : "hidden";

  // Create handlers for tab interactions
  const handlers = {
    onBack: goToWelcome,
    updateTab,
    createChatTab,
    closeTab,
    createProjectSettingsTab
  };

  // Use factory to create content
  const content = TabContentFactory.createContent(tab, handlers);

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.2 }}
      className={`h-full w-full ${panelVisibilityClass}`}
    >
      {content}
    </motion.div>
  );
};

/**
 * Main TabContent component - significantly simplified
 */
export const TabContent: React.FC = () => {
  const { 
    tabs, 
    activeTabId, 
    createChatTab, 
    findTabBySessionId, 
    createClaudeFileTab, 
    createAgentExecutionTab, 
    createCreateAgentTab, 
    createImportAgentTab, 
    closeTab, 
    updateTab 
  } = useTabState();
  
  const { subscribe, emit } = useEventBus();

  // Subscribe to type-safe events
  useEffect(() => {
    const subscriptions = [
      // Session events
      subscribe('session:open-in-tab', ({ session }) => {
        const existingTab = findTabBySessionId(session.id);
        if (existingTab) {
          updateTab(existingTab.id, {
            sessionData: session,
            title: session.project_path.split('/').pop() || 'Session'
          });
          emit('tab:switch', { tabId: existingTab.id });
        } else {
          const projectName = session.project_path.split('/').pop() || 'Session';
          const newTabId = createChatTab(session.id, projectName);
          updateTab(newTabId, {
            sessionData: session,
            initialProjectPath: session.project_path
          });
        }
      }),

      subscribe('session:selected', ({ session }) => {
        const existingTab = findTabBySessionId(session.id);
        if (existingTab) {
          updateTab(existingTab.id, {
            sessionData: session,
            title: session.project_path.split('/').pop() || 'Session',
          });
          emit('tab:switch', { tabId: existingTab.id });
        } else {
          const projectName = session.project_path.split('/').pop() || 'Session';
          const newTabId = createChatTab(session.id, projectName);
          updateTab(newTabId, {
            sessionData: session,
            initialProjectPath: session.project_path,
          });
        }
      }),

      // File events
      subscribe('file:open-claude', ({ file }) => {
        createClaudeFileTab(file.id, file.name || 'CLAUDE.md');
      }),

      // Agent events
      subscribe('agent:open-execution', ({ agent, tabId }) => {
        createAgentExecutionTab(agent, tabId);
      }),

      subscribe('agent:create-tab', () => {
        createCreateAgentTab();
      }),

      subscribe('agent:import-tab', () => {
        createImportAgentTab();
      }),

      // Tab events
      subscribe('tab:close', ({ tabId }) => {
        closeTab(tabId);
      })
    ];

    return () => {
      subscriptions.forEach(sub => sub.unsubscribe());
    };
  }, [subscribe, emit, createChatTab, findTabBySessionId, createClaudeFileTab, createAgentExecutionTab, createCreateAgentTab, createImportAgentTab, closeTab, updateTab]);
  
  return (
    <div className="flex-1 h-full relative">
      <AnimatePresence>
        {tabs.map((tab) => (
          <TabPanel
            key={tab.id}
            tab={tab}
            isActive={tab.id === activeTabId}
          />
        ))}
      </AnimatePresence>
      
      {tabs.length === 0 && (
        <Welcome />
      )}
    </div>
  );
};

export default TabContent;