import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Brain, 
  Search, 
  Save,
  Tag,
  Calendar,
  Download,
  Eye,
  AlertCircle,
  Loader2,
  Database,
  FileText,
  Lightbulb,
  Zap,
  Bug
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, Ta<PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { ClaudeFlowIntegration, ClaudeFlowMemoryEntry } from "@/lib/claudeFlowIntegration";
import { useClaudeFlowEvent } from "@/hooks/useClaudeFlowWebSocket";

interface ClaudeFlowMemoryProps {
  integration: ClaudeFlowIntegration;
}

/**
 * Claude Flow Memory Component
 * Manages agent memory storage, query, and visualization
 */
export const ClaudeFlowMemory: React.FC<ClaudeFlowMemoryProps> = ({
  integration
}) => {
  const [memories, setMemories] = useState<ClaudeFlowMemoryEntry[]>([]);
  const [selectedMemory, setSelectedMemory] = useState<ClaudeFlowMemoryEntry | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const [filterAgent, setFilterAgent] = useState<string>("all");
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newMemory, setNewMemory] = useState({
    agentId: "",
    sessionId: "",
    type: "observation" as const,
    content: "",
    tags: [] as string[],
    context: {}
  });
  const [tagInput, setTagInput] = useState("");

  // Subscribe to real-time memory updates
  useClaudeFlowEvent<ClaudeFlowMemoryEntry>('memory.stored', (memory) => {
    console.log('New memory stored:', memory);
    setMemories(prev => [memory, ...prev]);
  });

  useClaudeFlowEvent<{ entryId: string }>('memory.deleted', (data) => {
    console.log('Memory deleted:', data.entryId);
    setMemories(prev => prev.filter(m => m.id !== data.entryId));
    if (selectedMemory?.id === data.entryId) {
      setSelectedMemory(null);
    }
  });

  // Load memories on mount
  useEffect(() => {
    loadMemories();
  }, []);

  /**
   * Load memories from Claude Flow
   */
  const loadMemories = async () => {
    setLoading(true);
    setError(null);

    try {
      const entries = await integration.queryMemory({
        limit: 100
      });
      setMemories(entries);
    } catch (err) {
      console.error('Failed to load memories:', err);
      setError('Failed to load memory entries');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Search memories
   */
  const searchMemories = async () => {
    if (!searchQuery.trim()) {
      await loadMemories();
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const entries = await integration.queryMemory({
        search: searchQuery,
        type: filterType !== 'all' ? filterType : undefined,
        agentId: filterAgent !== 'all' ? filterAgent : undefined,
        limit: 100
      });
      setMemories(entries);
    } catch (err) {
      console.error('Failed to search memories:', err);
      setError('Failed to search memory entries');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Store new memory
   */
  const storeMemory = async () => {
    if (!newMemory.agentId || !newMemory.content) {
      setError('Agent ID and content are required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      await integration.storeMemory({
        agentId: newMemory.agentId,
        sessionId: newMemory.sessionId || `session-${Date.now()}`,
        type: newMemory.type,
        content: newMemory.content,
        tags: newMemory.tags,
        context: newMemory.context
      });

      // Reset form
      setNewMemory({
        agentId: "",
        sessionId: "",
        type: "observation",
        content: "",
        tags: [],
        context: {}
      });
      setTagInput("");
      setShowCreateDialog(false);

      // Reload memories
      await loadMemories();
    } catch (err) {
      console.error('Failed to store memory:', err);
      setError('Failed to store memory entry');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Add tag to new memory
   */
  const addTag = () => {
    if (tagInput.trim() && !newMemory.tags.includes(tagInput.trim())) {
      setNewMemory(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput("");
    }
  };

  /**
   * Remove tag from new memory
   */
  const removeTag = (tag: string) => {
    setNewMemory(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  /**
   * Get memory type icon
   */
  const getMemoryTypeIcon = (type: string) => {
    switch (type) {
      case 'observation': return <Eye className="h-4 w-4" />;
      case 'insight': return <Lightbulb className="h-4 w-4" />;
      case 'decision': return <Zap className="h-4 w-4" />;
      case 'artifact': return <FileText className="h-4 w-4" />;
      case 'error': return <Bug className="h-4 w-4" />;
      default: return <Database className="h-4 w-4" />;
    }
  };

  /**
   * Get memory type color
   */
  const getMemoryTypeColor = (type: string) => {
    switch (type) {
      case 'observation': return 'text-blue-500';
      case 'insight': return 'text-yellow-500';
      case 'decision': return 'text-purple-500';
      case 'artifact': return 'text-green-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  /**
   * Get filtered memories
   */
  const filteredMemories = memories.filter(memory => {
    if (filterType !== 'all' && memory.type !== filterType) return false;
    if (filterAgent !== 'all' && memory.agentId !== filterAgent) return false;
    return true;
  });

  /**
   * Get unique agents from memories
   */
  const uniqueAgents = Array.from(new Set(memories.map(m => m.agentId)));

  return (
    <Card className="border-purple-500/20 bg-gradient-to-br from-purple-500/5 to-pink-500/5">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5 text-purple-500" />
          Agent Memory System
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search and Filter Controls */}
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 flex gap-2">
              <Input
                placeholder="Search memories..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && searchMemories()}
                className="flex-1"
              />
              <Button onClick={searchMemories} disabled={loading} className="gap-2">
                <Search className="h-4 w-4" />
                Search
              </Button>
            </div>
            <div className="flex gap-2">
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="observation">Observations</SelectItem>
                  <SelectItem value="insight">Insights</SelectItem>
                  <SelectItem value="decision">Decisions</SelectItem>
                  <SelectItem value="artifact">Artifacts</SelectItem>
                  <SelectItem value="error">Errors</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterAgent} onValueChange={setFilterAgent}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by agent" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Agents</SelectItem>
                  {uniqueAgents.map(agentId => (
                    <SelectItem key={agentId} value={agentId}>
                      {agentId}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button onClick={() => setShowCreateDialog(true)} className="gap-2">
                <Save className="h-4 w-4" />
                Store Memory
              </Button>
            </div>
          </div>

          {/* Memory List */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {/* Memory Items */}
            <Card className="border-border/20">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center justify-between">
                  <span>Memory Entries ({filteredMemories.length})</span>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={loadMemories}
                    disabled={loading}
                  >
                    {loading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Download className="h-4 w-4" />
                    )}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[500px]">
                  <div className="space-y-2">
                    {loading && filteredMemories.length === 0 ? (
                      <div className="text-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground" />
                        <p className="text-muted-foreground">Loading memories...</p>
                      </div>
                    ) : filteredMemories.length > 0 ? (
                      filteredMemories.map((memory) => (
                        <motion.div
                          key={memory.id}
                          initial={{ opacity: 0, y: -10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className={`p-3 rounded-lg bg-card/50 border border-border/10 hover:border-purple-500/20 transition-colors cursor-pointer ${
                            selectedMemory?.id === memory.id ? 'border-purple-500/40 bg-purple-500/5' : ''
                          }`}
                          onClick={() => setSelectedMemory(memory)}
                        >
                          <div className="flex items-start gap-3">
                            <div className={`mt-0.5 ${getMemoryTypeColor(memory.type)}`}>
                              {getMemoryTypeIcon(memory.type)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <Badge variant="outline" className="text-xs">
                                  {memory.type}
                                </Badge>
                                <span className="text-xs text-muted-foreground">
                                  {memory.agentId}
                                </span>
                              </div>
                              <p className="text-sm line-clamp-2">{memory.content}</p>
                              {memory.tags.length > 0 && (
                                <div className="flex items-center gap-1 mt-2">
                                  <Tag className="h-3 w-3 text-muted-foreground" />
                                  {memory.tags.map(tag => (
                                    <Badge key={tag} variant="secondary" className="text-xs">
                                      {tag}
                                    </Badge>
                                  ))}
                                </div>
                              )}
                              <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                                <Calendar className="h-3 w-3" />
                                {new Date(memory.timestamp).toLocaleString()}
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      ))
                    ) : (
                      <div className="text-center py-8">
                        <Brain className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">No Memories Found</h3>
                        <p className="text-muted-foreground">
                          {searchQuery ? 'Try adjusting your search criteria' : 'Store some memories to get started'}
                        </p>
                      </div>
                    )}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Memory Details */}
            <Card className="border-border/20">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Memory Details</CardTitle>
              </CardHeader>
              <CardContent>
                {selectedMemory ? (
                  <div className="space-y-4">
                    <div>
                      <Label className="text-xs text-muted-foreground">Type</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <div className={getMemoryTypeColor(selectedMemory.type)}>
                          {getMemoryTypeIcon(selectedMemory.type)}
                        </div>
                        <Badge variant="outline">{selectedMemory.type}</Badge>
                      </div>
                    </div>

                    <div>
                      <Label className="text-xs text-muted-foreground">Agent</Label>
                      <p className="text-sm font-medium mt-1">{selectedMemory.agentId}</p>
                    </div>

                    <div>
                      <Label className="text-xs text-muted-foreground">Session</Label>
                      <p className="text-sm font-mono mt-1">{selectedMemory.sessionId}</p>
                    </div>

                    <div>
                      <Label className="text-xs text-muted-foreground">Content</Label>
                      <Card className="mt-1 p-3 bg-background/50">
                        <p className="text-sm whitespace-pre-wrap">{selectedMemory.content}</p>
                      </Card>
                    </div>

                    {selectedMemory.tags.length > 0 && (
                      <div>
                        <Label className="text-xs text-muted-foreground">Tags</Label>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {selectedMemory.tags.map(tag => (
                            <Badge key={tag} variant="secondary">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {selectedMemory.context && Object.keys(selectedMemory.context).length > 0 && (
                      <div>
                        <Label className="text-xs text-muted-foreground">Context</Label>
                        <Card className="mt-1 p-3 bg-background/50">
                          <pre className="text-xs overflow-x-auto">
                            {JSON.stringify(selectedMemory.context, null, 2)}
                          </pre>
                        </Card>
                      </div>
                    )}

                    <div>
                      <Label className="text-xs text-muted-foreground">Timestamp</Label>
                      <p className="text-sm mt-1">
                        {new Date(selectedMemory.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Eye className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium mb-2">Select a Memory</h3>
                    <p className="text-muted-foreground">
                      Click on a memory entry to view its details
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Error Display */}
          <AnimatePresence>
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="p-3 rounded-lg bg-destructive/10 border border-destructive/20 flex items-start gap-2 text-sm text-destructive"
              >
                <AlertCircle className="h-4 w-4 flex-shrink-0 mt-0.5" />
                <span>{error}</span>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </CardContent>

      {/* Create Memory Dialog */}
      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Store New Memory</DialogTitle>
            <DialogDescription>
              Create a new memory entry for an agent
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="agentId">Agent ID</Label>
                <Input
                  id="agentId"
                  value={newMemory.agentId}
                  onChange={(e) => setNewMemory({...newMemory, agentId: e.target.value})}
                  placeholder="agent-123"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="sessionId">Session ID</Label>
                <Input
                  id="sessionId"
                  value={newMemory.sessionId}
                  onChange={(e) => setNewMemory({...newMemory, sessionId: e.target.value})}
                  placeholder="session-456 (optional)"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Memory Type</Label>
              <Select
                value={newMemory.type}
                onValueChange={(value: any) => setNewMemory({...newMemory, type: value})}
              >
                <SelectTrigger id="type">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="observation">Observation</SelectItem>
                  <SelectItem value="insight">Insight</SelectItem>
                  <SelectItem value="decision">Decision</SelectItem>
                  <SelectItem value="artifact">Artifact</SelectItem>
                  <SelectItem value="error">Error</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="content">Content</Label>
              <Textarea
                id="content"
                value={newMemory.content}
                onChange={(e) => setNewMemory({...newMemory, content: e.target.value})}
                placeholder="Enter memory content..."
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label>Tags</Label>
              <div className="flex gap-2">
                <Input
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                  placeholder="Add tag..."
                />
                <Button type="button" onClick={addTag} size="sm">
                  Add
                </Button>
              </div>
              {newMemory.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {newMemory.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="gap-1">
                      {tag}
                      <button
                        onClick={() => removeTag(tag)}
                        className="ml-1 hover:text-destructive"
                      >
                        ×
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
              Cancel
            </Button>
            <Button onClick={storeMemory} disabled={loading}>
              {loading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Storing...
                </>
              ) : (
                'Store Memory'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  );
};

export default ClaudeFlowMemory;