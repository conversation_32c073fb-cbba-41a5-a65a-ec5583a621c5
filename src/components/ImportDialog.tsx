import { useState, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Upload, FileJson, AlertCircle, CheckCircle } from 'lucide-react';
import { ImportOptions, ImportResult } from '@/lib/exportImportEngine';

interface ImportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImport: (file: File, options: ImportOptions) => Promise<ImportResult>;
}

export function ImportDialog({
  open,
  onOpenChange,
  onImport
}: ImportDialogProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [overwriteExisting, setOverwriteExisting] = useState(false);
  const [mergeConflicts, setMergeConflicts] = useState<'skip' | 'overwrite' | 'rename'>('skip');
  const [validateSchema, setValidateSchema] = useState(true);
  const [createBackup, setCreateBackup] = useState(true);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const validTypes = ['.json', '.zip'];
      const fileExtension = file.name.substring(file.name.lastIndexOf('.'));
      
      if (!validTypes.includes(fileExtension)) {
        setError('Invalid file type. Please select a JSON or ZIP file.');
        return;
      }
      
      setSelectedFile(file);
      setError(null);
      setImportResult(null);
    }
  }, []);

  const handleImport = async () => {
    if (!selectedFile) return;

    setIsImporting(true);
    setImportProgress(0);
    setError(null);

    try {
      const options: ImportOptions = {
        overwriteExisting,
        mergeConflicts,
        validateSchema,
        createBackup
      };

      // Simulate progress updates
      const progressInterval = setInterval(() => {
        setImportProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const result = await onImport(selectedFile, options);
      
      clearInterval(progressInterval);
      setImportProgress(100);
      setImportResult(result);

      if (result.success) {
        setTimeout(() => {
          onOpenChange(false);
          resetState();
        }, 2000);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Import failed');
    } finally {
      setIsImporting(false);
    }
  };

  const resetState = () => {
    setSelectedFile(null);
    setOverwriteExisting(false);
    setMergeConflicts('skip');
    setValidateSchema(true);
    setCreateBackup(true);
    setImportProgress(0);
    setImportResult(null);
    setError(null);
  };

  const handleClose = () => {
    if (!isImporting) {
      onOpenChange(false);
      resetState();
    }
  };

  const renderImportResult = () => {
    if (!importResult) return null;

    const totalImported = 
      importResult.imported.settings +
      importResult.imported.agents +
      importResult.imported.tasks +
      importResult.imported.memory;

    return (
      <div className="space-y-3">
        <Alert className={importResult.success ? "border-green-500" : "border-red-500"}>
          <div className="flex items-center gap-2">
            {importResult.success ? (
              <CheckCircle className="h-4 w-4 text-green-500" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-500" />
            )}
            <AlertDescription>
              {importResult.success 
                ? `Successfully imported ${totalImported} items`
                : 'Import completed with errors'
              }
            </AlertDescription>
          </div>
        </Alert>

        {importResult.success && (
          <div className="grid grid-cols-2 gap-2 text-sm">
            {importResult.imported.settings > 0 && (
              <div>Settings: {importResult.imported.settings}</div>
            )}
            {importResult.imported.agents > 0 && (
              <div>Agents: {importResult.imported.agents}</div>
            )}
            {importResult.imported.tasks > 0 && (
              <div>Tasks: {importResult.imported.tasks}</div>
            )}
            {importResult.imported.memory > 0 && (
              <div>Memory entries: {importResult.imported.memory}</div>
            )}
          </div>
        )}

        {importResult.conflicts.length > 0 && (
          <div className="space-y-1">
            <p className="text-sm font-medium">Conflicts resolved:</p>
            <p className="text-sm text-muted-foreground">
              {importResult.conflicts.length} conflicts were {mergeConflicts}
            </p>
          </div>
        )}

        {importResult.errors.length > 0 && (
          <div className="space-y-1">
            <p className="text-sm font-medium text-red-500">Errors:</p>
            {importResult.errors.slice(0, 3).map((error, index) => (
              <p key={index} className="text-sm text-red-500">
                • {error.message}
              </p>
            ))}
            {importResult.errors.length > 3 && (
              <p className="text-sm text-muted-foreground">
                ...and {importResult.errors.length - 3} more errors
              </p>
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Import Claude Flow Data</DialogTitle>
          <DialogDescription>
            Import configurations, agents, tasks, and memory from a backup file.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* File Selection */}
          <div className="space-y-2">
            <Label htmlFor="import-file">Select Import File</Label>
            <div className="flex items-center gap-2">
              <input
                id="import-file"
                type="file"
                accept=".json,.zip"
                onChange={handleFileSelect}
                className="hidden"
                disabled={isImporting}
              />
              <Button
                variant="outline"
                onClick={() => document.getElementById('import-file')?.click()}
                disabled={isImporting}
                className="w-full justify-start gap-2"
              >
                <Upload className="h-4 w-4" />
                {selectedFile ? selectedFile.name : 'Choose file...'}
              </Button>
            </div>
            {selectedFile && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <FileJson className="h-3 w-3" />
                {(selectedFile.size / 1024).toFixed(2)} KB
              </div>
            )}
          </div>

          {/* Import Options */}
          <div className="space-y-3">
            <Label className="text-base font-medium">Import Options</Label>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="validate"
                  checked={validateSchema}
                  onCheckedChange={(checked) => setValidateSchema(checked as boolean)}
                  disabled={isImporting}
                />
                <Label
                  htmlFor="validate"
                  className="text-sm font-normal cursor-pointer"
                >
                  Validate data before import
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="backup"
                  checked={createBackup}
                  onCheckedChange={(checked) => setCreateBackup(checked as boolean)}
                  disabled={isImporting}
                />
                <Label
                  htmlFor="backup"
                  className="text-sm font-normal cursor-pointer"
                >
                  Create backup before import
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="overwrite"
                  checked={overwriteExisting}
                  onCheckedChange={(checked) => setOverwriteExisting(checked as boolean)}
                  disabled={isImporting}
                />
                <Label
                  htmlFor="overwrite"
                  className="text-sm font-normal cursor-pointer"
                >
                  Overwrite existing data
                </Label>
              </div>
            </div>
          </div>

          {/* Conflict Resolution */}
          <div className="space-y-2">
            <Label htmlFor="conflicts">Conflict Resolution</Label>
            <Select 
              value={mergeConflicts} 
              onValueChange={(value: any) => setMergeConflicts(value)}
              disabled={isImporting}
            >
              <SelectTrigger id="conflicts">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="skip">Skip conflicting items</SelectItem>
                <SelectItem value="overwrite">Overwrite existing items</SelectItem>
                <SelectItem value="rename">Rename conflicting items</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Progress */}
          {isImporting && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Importing...</span>
                <span>{importProgress}%</span>
              </div>
              <Progress value={importProgress} className="h-2" />
            </div>
          )}

          {/* Error Display */}
          {error && !isImporting && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Import Result */}
          {importResult && !isImporting && renderImportResult()}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isImporting}
          >
            {importResult?.success ? 'Close' : 'Cancel'}
          </Button>
          {!importResult && (
            <Button
              onClick={handleImport}
              disabled={!selectedFile || isImporting}
              className="gap-2"
            >
              <Upload className="h-4 w-4" />
              {isImporting ? 'Importing...' : 'Import'}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}