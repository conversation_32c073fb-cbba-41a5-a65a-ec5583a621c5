import React, { useState, useEffect } from "react";
import { useTabState } from "@/hooks/useTabState";
import { useTheme } from "@/hooks/useTheme";
import { motion } from "framer-motion";
import { 
  MessageSquare, 
  Folder, 
  Server, 
  Settings, 
  ArrowRight,
  Zap,
  Code2,
  Bot,
  FileText,
  Clock,
  KeyRound,
  Crown,
  Rabbit,
  Sun,
  Moon,
  Palette,
  Monitor,
  Activity,
  TrendingUp,
  Users,
  PlayCircle,
  GitBranch
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { KitsuneLogo } from "./KitsuneLogo";
import "@/assets/shimmer.css";

interface WelcomeProps {}

export function Welcome({}: WelcomeProps) {
  const { 
    createChatTab, 
    createProjectsTab, 
    createMCPTab,
    createSettingsTab,
    createCCAgentsTab
  } = useTabState();
  
  const { theme, setTheme, isLoading: themeLoading } = useTheme();
  const [stats, setStats] = useState({
    totalSessions: 12,
    activeProjects: 4,
    mcpServers: 3,
    recentActivity: 8
  });

  const [recentActivity, setRecentActivity] = useState([
    { id: 1, type: 'session', title: 'React Component Analysis', time: '2 hours ago', icon: MessageSquare },
    { id: 2, type: 'project', title: 'Claude Dashboard Redesign', time: '4 hours ago', icon: Folder },
    { id: 3, type: 'agent', title: 'Frontend Agent Updated', time: '1 day ago', icon: Bot },
    { id: 4, type: 'mcp', title: 'Sequential Server Connected', time: '2 days ago', icon: Server }
  ]);

  // Theme toggle with cycle through all themes
  const getThemeIcon = (currentTheme: string) => {
    switch (currentTheme) {
      case 'light': return Sun;
      case 'dark': return Moon;
      case 'gray': return Monitor;
      case 'custom': return Palette;
      default: return Moon;
    }
  };

  const cycleTheme = () => {
    const themes = ['dark', 'gray', 'light', 'custom'] as const;
    const currentIndex = themes.indexOf(theme as any);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]);
  };

  const quickActions = [
    {
      id: 'new-session',
      title: 'New Session',
      description: 'Start a new Claude Code conversation down the rabbit hole',
      icon: MessageSquare,
      color: 'from-slate-800/40 to-gray-900/40',
      borderColor: 'border-slate-600/50',
      action: () => createChatTab(),
      ariaLabel: 'Start a new AI coding session',
    },
    {
      id: 'browse-projects',
      title: 'Browse Projects',
      description: 'View and manage your wonderland of code projects',
      icon: Folder,
      color: 'from-indigo-900/40 to-purple-900/40',
      borderColor: 'border-indigo-600/50',
      action: () => createProjectsTab(),
      ariaLabel: 'Browse and manage projects',
    },
    {
      id: 'cc-agents',
      title: 'CC Agents',
      description: 'Create and manage your Cheshire Cat coding assistants',
      icon: Bot,
      color: 'from-purple-900/40 to-violet-900/40',
      borderColor: 'border-purple-600/50',
      action: () => createCCAgentsTab(),
      ariaLabel: 'Create and manage AI coding agents',
    },
    {
      id: 'mcp-servers',
      title: 'MCP Servers',
      description: 'Manage your Mad Hatter\'s tea party of protocols',
      icon: Server,
      color: 'from-teal-900/40 to-cyan-900/40',
      borderColor: 'border-teal-600/50',
      action: () => createMCPTab(),
      ariaLabel: 'Manage MCP server integrations',
    },
    {
      id: 'settings',
      title: 'Settings',
      description: 'Configure your Wonderland preferences',
      icon: Settings,
      color: 'from-gray-800/40 to-slate-900/40',
      borderColor: 'border-gray-600/50',
      action: () => createSettingsTab(),
      ariaLabel: 'Configure application settings',
    },
  ];

  const features = [
    { icon: Clock, text: 'Down the rabbit hole fast', ariaLabel: 'AI assistance feature' },
    { icon: KeyRound, text: 'Unlock coding mysteries', ariaLabel: 'Code understanding feature' },
    { icon: Crown, text: 'Rule your code kingdom', ariaLabel: 'Custom AI agents feature' },
    { icon: Rabbit, text: 'Follow the white rabbit', ariaLabel: 'Project context feature' },
  ];

  return (
    <motion.div 
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      className="h-full w-full bg-background text-foreground overflow-y-auto"
      role="main"
      aria-label="Dashboard - Ahmed's La La Land"
    >
      <div className="max-w-7xl mx-auto px-4 py-6 space-y-8">
        {/* Dashboard Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col md:flex-row md:items-center justify-between gap-4"
        >
          <div className="flex items-center gap-4">
            {/* Interactive Animated Logo */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 150 }}
              className="group cursor-pointer relative"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => createChatTab()}
            >
              <video
                src="/1af69824-e347-454e-bd2f-13c66a8f0a56.mp4"
                width={48}
                height={48}
                autoPlay
                loop
                muted
                playsInline
                className="rounded-lg shadow-lg transition-all duration-300 group-hover:shadow-primary/25"
                aria-label="Ahmed's La La Land - Click to start new session"
              />
              <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary/0 to-primary/0 group-hover:from-primary/10 group-hover:to-primary/10 transition-all duration-300" />
            </motion.div>
            
            <div>
              <h1 className="text-2xl md:text-3xl font-bold">
                Welcome to{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 via-pink-500 to-indigo-500">
                  Ahmed's La La Land
                </span>
              </h1>
              <p className="text-sm text-muted-foreground italic">
                "We're all MAD here." - Your Intelligence and Madness are the SAME
              </p>
            </div>
          </div>
          
          {/* Theme Toggle & Actions */}
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={cycleTheme}
              disabled={themeLoading}
              className="gap-2"
              aria-label={`Current theme: ${theme}. Click to cycle themes.`}
            >
              {React.createElement(getThemeIcon(theme), { className: "w-4 h-4" })}
              <span className="capitalize hidden sm:inline">{theme}</span>
            </Button>
            
            <Button
              size="sm"
              onClick={() => createChatTab()}
              className="gap-2"
            >
              <PlayCircle className="w-4 h-4" />
              <span className="hidden sm:inline">New Session</span>
            </Button>
          </div>
        </motion.div>

        {/* Stats Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4"
        >
          {[
            { label: "Active Sessions", value: stats.totalSessions, icon: Activity, color: "text-green-500", change: "+3" },
            { label: "Projects", value: stats.activeProjects, icon: Folder, color: "text-blue-500", change: "+1" },
            { label: "MCP Servers", value: stats.mcpServers, icon: Server, color: "text-purple-500", change: "0" },
            { label: "Recent Activity", value: stats.recentActivity, icon: TrendingUp, color: "text-orange-500", change: "+2" }
          ].map((stat, index) => (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 * index }}
            >
              <Card className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">{stat.label}</p>
                      <div className="flex items-center gap-2">
                        <p className="text-2xl font-bold">{stat.value}</p>
                        <Badge variant="secondary" className="text-xs">
                          {stat.change}
                        </Badge>
                      </div>
                    </div>
                    <stat.icon className={`w-5 h-5 ${stat.color}`} />
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  Quick Actions
                </CardTitle>
                <CardDescription>
                  Jump into your most common workflows
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {quickActions.map((action, index) => (
                    <motion.button
                      key={action.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 + index * 0.05 }}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={action.action}
                      className="group p-4 rounded-lg border border-border/60 hover:border-border/80 bg-card hover:bg-accent/50 text-left transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary/50"
                      aria-label={action.ariaLabel}
                    >
                      <div className="flex items-start gap-3">
                        <div className="p-2 rounded-md bg-primary/10 group-hover:bg-primary/20 transition-colors">
                          <action.icon className="w-5 h-5 text-primary" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-sm group-hover:text-primary transition-colors">
                            {action.title}
                          </h3>
                          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                            {action.description}
                          </p>
                        </div>
                        <ArrowRight className="w-4 h-4 text-muted-foreground group-hover:text-primary transition-colors opacity-0 group-hover:opacity-100" />
                      </div>
                    </motion.button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  Recent Activity
                </CardTitle>
                <CardDescription>
                  Your latest work and updates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentActivity.map((activity) => (
                    <motion.div
                      key={activity.id}
                      initial={{ opacity: 0, x: 10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4 + activity.id * 0.05 }}
                      className="flex items-start gap-3 p-2 rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                    >
                      <div className="p-1.5 rounded-md bg-primary/10">
                        <activity.icon className="w-3 h-3 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{activity.title}</p>
                        <p className="text-xs text-muted-foreground">{activity.time}</p>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>

        {/* Features & Shortcuts */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="w-5 h-5" />
                Features & Shortcuts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2 mb-4">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.5 + index * 0.05 }}
                    className="flex items-center gap-2 px-3 py-1.5 rounded-full bg-primary/10 text-primary text-sm"
                  >
                    <feature.icon className="w-3 h-3" />
                    <span>{feature.text}</span>
                  </motion.div>
                ))}
              </div>
              
              <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <kbd className="px-2 py-1 text-xs bg-muted rounded border">⌘ T</kbd>
                  <span>New Session</span>
                </div>
                <div className="flex items-center gap-2">
                  <kbd className="px-2 py-1 text-xs bg-muted rounded border">⌘ P</kbd>
                  <span>Projects</span>
                </div>
                <div className="flex items-center gap-2">
                  <kbd className="px-2 py-1 text-xs bg-muted rounded border">⌘ ,</kbd>
                  <span>Settings</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </motion.div>
  );
}