import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  FileText,
  Settings,
  ExternalLink,
  Network,
  Info,
  Bot,
  Folder,
  Menu,
  X,
  CheckCircle2,
  AlertCircle,
  Sparkles,
  Terminal
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Popover } from "@/components/ui/popover";
import { api, type ClaudeVersionStatus } from "@/lib/api";
import { cn } from "@/lib/utils";

interface TopbarProps {
  onProjectsClick: () => void;
  onClaudeClick: () => void;
  onSettingsClick: () => void;
  onMCPClick: () => void;
  onInfoClick: () => void;
  onAgentsClick?: () => void;
  onClaudeFlowClick: () => void;
  className?: string;
}

// Navigation items configuration
const navItems = [
  { id: "projects", icon: Folder, label: "Projects", glow: "#8B5CF6" },
  { id: "agents", icon: Bo<PERSON>, label: "Agents", glow: "#10B981", optional: true },
  { id: "claude-flow", icon: Bot, label: "Claude-Flow", glow: "#7C3AED" },
  { id: "claude", icon: FileText, label: "CLAUDE.md", glow: "#F59E0B" },
  { id: "mcp", icon: Network, label: "MCP", glow: "#EF4444" },
  { id: "settings", icon: Settings, label: "Settings", glow: "#6B7280" },
];

export const TopbarModern: React.FC<TopbarProps> = ({
  onProjectsClick,
  onClaudeClick,
  onSettingsClick,
  onMCPClick,
  onInfoClick,
  onAgentsClick,
  onClaudeFlowClick,
  className,
}) => {
  const [versionStatus, setVersionStatus] = useState<ClaudeVersionStatus | null>(null);
  const [checking, setChecking] = useState(true);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  
  useEffect(() => {
    checkVersion();
  }, []);
  
  const checkVersion = async () => {
    try {
      setChecking(true);
      const status = await api.checkClaudeVersion();
      setVersionStatus(status);
      
      if (!status.is_installed && status.output.includes("No such file or directory")) {
        window.dispatchEvent(new CustomEvent('claude-not-found'));
      }
    } catch (err) {
      console.error("Failed to check Claude version:", err);
      setVersionStatus({
        is_installed: false,
        output: "Failed to check version",
      });
    } finally {
      setChecking(false);
    }
  };
  
  const handleNavClick = (id: string) => {
    setMobileMenuOpen(false);
    switch (id) {
      case "projects": onProjectsClick(); break;
      case "agents": onAgentsClick?.(); break;
      case "claude-flow": onClaudeFlowClick(); break;
      case "claude": onClaudeClick(); break;
      case "mcp": onMCPClick(); break;
      case "settings": onSettingsClick(); break;
    }
  };
  
  const StatusIndicator = () => {
    if (checking) {
      return (
        <motion.div 
          className="flex items-center space-x-3 px-4 py-2 rounded-xl glass-subtle"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
          >
            <Sparkles className="h-4 w-4 text-primary" />
          </motion.div>
          <span className="text-sm font-medium gradient-text">Checking...</span>
        </motion.div>
      );
    }
    
    if (!versionStatus) return null;
    
    const isInstalled = versionStatus.is_installed;
    const Icon = isInstalled ? CheckCircle2 : AlertCircle;
    const color = isInstalled ? "text-green-500" : "text-orange-500";
    const bgColor = isInstalled ? "bg-green-500/10" : "bg-orange-500/10";
    
    const statusContent = (
      <motion.button
        onClick={onSettingsClick}
        className={cn(
          "flex items-center space-x-3 px-4 py-2 rounded-xl transition-all",
          "hover:scale-105 cursor-pointer group",
          bgColor,
          "backdrop-blur-xl border border-white/5"
        )}
        whileHover={{ y: -2 }}
        whileTap={{ scale: 0.98 }}
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 500, damping: 20 }}
        >
          <Icon className={cn("h-4 w-4", color)} />
        </motion.div>
        <div className="flex flex-col items-start">
          <div className="flex items-center gap-1">
            <span className="text-xs font-semibold text-foreground/90">
              Claude Code
            </span>
            <motion.span
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.8, 1, 0.8],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              className="text-xs"
            >
              💗
            </motion.span>
          </div>
          {isInstalled && versionStatus.version && (
            <span className="text-[10px] text-muted-foreground">
              v{versionStatus.version}
            </span>
          )}
        </div>
        <motion.div
          className="h-2 w-2 rounded-full"
          animate={{
            backgroundColor: isInstalled ? "#10B981" : "#F59E0B",
            scale: [1, 1.2, 1],
          }}
          transition={{
            scale: {
              duration: 2,
              repeat: Infinity,
              repeatType: "reverse",
            },
          }}
        />
      </motion.button>
    );
    
    if (!isInstalled) {
      return (
        <Popover
          trigger={statusContent}
          content={
            <motion.div 
              className="space-y-4 max-w-xs"
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-orange-500" />
                <p className="text-sm font-semibold">Claude Code not found</p>
              </div>
              <div className="rounded-lg bg-muted/50 p-3">
                <pre className="text-xs font-mono whitespace-pre-wrap text-muted-foreground">
                  {versionStatus.output}
                </pre>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="w-full hover-lift"
                onClick={onSettingsClick}
              >
                Select Installation
              </Button>
              <a
                href="https://www.anthropic.com/claude-code"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center space-x-2 text-xs text-primary hover:underline group"
              >
                <span>Install Claude Code</span>
                <ExternalLink className="h-3 w-3 transition-transform group-hover:translate-x-1" />
              </a>
            </motion.div>
          }
          align="start"
        />
      );
    }
    
    return statusContent;
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: [0.23, 1, 0.32, 1] }}
      className={cn(
        "relative z-50",
        className
      )}
    >
      {/* Backdrop blur layer */}
      <div className="absolute inset-0 bg-background/60 backdrop-blur-2xl border-b border-white/5" />
      
      {/* Content */}
      <div className="relative flex items-center justify-between px-4 md:px-6 py-3">
        {/* Status Indicator */}
        <StatusIndicator />
        
        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center">
          <div className="flex items-center p-1 rounded-2xl glass-subtle">
            {navItems.map((item, index) => {
              if (item.optional && item.id === "agents" && !onAgentsClick) return null;
              
              const Icon = item.icon;
              const isActive = hoveredItem === item.id;
              
              return (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <motion.button
                    onClick={() => handleNavClick(item.id)}
                    onMouseEnter={() => setHoveredItem(item.id)}
                    onMouseLeave={() => setHoveredItem(null)}
                    className={cn(
                      "relative px-4 py-2 text-sm font-medium rounded-xl",
                      "transition-all duration-200",
                      "hover:text-foreground text-muted-foreground",
                      "group"
                    )}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {/* Glow effect */}
                    <AnimatePresence>
                      {isActive && (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          exit={{ opacity: 0 }}
                          className="absolute inset-0 rounded-xl"
                          style={{
                            background: `radial-gradient(circle at center, ${item.glow}20 0%, transparent 70%)`,
                          }}
                        />
                      )}
                    </AnimatePresence>
                    
                    {/* Content */}
                    <span className="relative flex items-center space-x-2">
                      <Icon className={cn(
                        "h-4 w-4 transition-transform",
                        isActive && "scale-110"
                      )} />
                      <span>{item.label}</span>
                    </span>
                    
                    {/* Hover indicator */}
                    <motion.div
                      className="absolute bottom-0 left-1/2 h-0.5 bg-gradient-to-r from-transparent via-current to-transparent"
                      initial={{ width: 0, x: "-50%" }}
                      animate={{ 
                        width: isActive ? "80%" : 0,
                        x: "-50%"
                      }}
                      transition={{ duration: 0.2 }}
                    />
                  </motion.button>
                </motion.div>
              );
            })}
          </div>
          
          {/* Info Button */}
          <motion.button
            onClick={onInfoClick}
            className="ml-3 p-2 rounded-xl glass-subtle hover:bg-accent/20 transition-all"
            whileHover={{ scale: 1.1, rotate: 15 }}
            whileTap={{ scale: 0.9 }}
          >
            <Info className="h-4 w-4" />
          </motion.button>
        </nav>
        
        {/* Mobile Menu Button */}
        <motion.button
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          className="md:hidden p-2 rounded-xl glass-subtle hover:bg-accent/20"
          whileTap={{ scale: 0.9 }}
        >
          <AnimatePresence mode="wait">
            {mobileMenuOpen ? (
              <motion.div
                key="close"
                initial={{ rotate: -90 }}
                animate={{ rotate: 0 }}
                exit={{ rotate: 90 }}
              >
                <X className="h-5 w-5" />
              </motion.div>
            ) : (
              <motion.div
                key="menu"
                initial={{ rotate: 90 }}
                animate={{ rotate: 0 }}
                exit={{ rotate: -90 }}
              >
                <Menu className="h-5 w-5" />
              </motion.div>
            )}
          </AnimatePresence>
        </motion.button>
      </div>
      
      {/* Mobile Menu */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-background/80 backdrop-blur-xl z-40 md:hidden"
              onClick={() => setMobileMenuOpen(false)}
            />
            
            {/* Menu */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              className="absolute top-full left-0 right-0 mt-2 mx-4 p-2 rounded-2xl glass border border-white/10 z-50 md:hidden"
            >
              {navItems.map((item, index) => {
                if (item.optional && item.id === "agents" && !onAgentsClick) return null;
                
                const Icon = item.icon;
                
                return (
                  <motion.button
                    key={item.id}
                    onClick={() => handleNavClick(item.id)}
                    className={cn(
                      "w-full flex items-center space-x-3 px-4 py-3 rounded-xl",
                      "text-left transition-all",
                      "hover:bg-accent/10 hover:pl-6"
                    )}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <Icon className="h-5 w-5 text-muted-foreground" />
                    <span className="font-medium">{item.label}</span>
                    <motion.div
                      className="ml-auto h-2 w-2 rounded-full"
                      style={{ backgroundColor: item.glow }}
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    />
                  </motion.button>
                );
              })}
              
              <div className="my-2 h-px bg-gradient-to-r from-transparent via-border to-transparent" />
              
              <motion.button
                onClick={() => {
                  setMobileMenuOpen(false);
                  onInfoClick();
                }}
                className="w-full flex items-center space-x-3 px-4 py-3 rounded-xl hover:bg-accent/10"
                whileTap={{ scale: 0.98 }}
              >
                <Info className="h-5 w-5 text-muted-foreground" />
                <span className="font-medium">About</span>
              </motion.button>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </motion.div>
  );
};