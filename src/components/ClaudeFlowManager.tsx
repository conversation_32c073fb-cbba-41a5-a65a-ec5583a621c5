import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ClaudeFlowErrorBoundary } from "./ClaudeFlowErrorBoundary";
import { useClaudeFlowError } from "@/hooks/useClaudeFlowError";
import { useClaudeFlowKeyboardShortcuts, ClaudeFlowKeyboardActions } from "@/hooks/useClaudeFlowKeyboardShortcuts";
import { ClaudeFlowKeyboardShortcutsHelp, ClaudeFlowActionWithShortcut } from "./ClaudeFlowKeyboardShortcutsHelp";
import { useSearch } from "@/hooks/useSearch";
import { SearchBar } from "./SearchBar";
import { FilterPanel } from "./FilterPanel";
import { BulkActionBar } from "./BulkActionBar";
import { BulkOperationDialog } from "./BulkOperationDialog";
import { useBulkOperations } from "@/hooks/useBulkOperations";
import { Checkbox } from "@/components/ui/checkbox";
import { SearchEngine } from "@/lib/searchEngine";
import { ExportDialog } from "./ExportDialog";
import { ImportDialog } from "./ImportDialog";
import { ExportImportEngine, ExportOptions, ImportOptions, ImportResult } from "@/lib/exportImportEngine";
import { 
  ArrowLeft, 
  Bot, 
  Zap, 
  Play, 
  Pause, 
  Square, 
  Plus, 
  Trash2, 
  Activity, 
  Brain, 
  Settings, 
  Monitor,
  Terminal,
  Network,
  Clock,
  XCircle,
  AlertCircle,
  Loader2,
  RefreshCw,
  Search,
  Code,
  FileText,
  Shield,
  GitBranch,
  Keyboard,
  LayoutTemplate,
  Sparkles,
  Database,
  TestTube,
  Bug,
  BookOpen,
  TrendingUp,
  Filter,
  X,
  Download,
  Upload
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Slider } from "@/components/ui/slider";
import { Toast, ToastContainer } from "@/components/ui/toast";
import { ClaudeFlowTerminal } from "./ClaudeFlowTerminal";
import { ClaudeFlowMonitoring } from "./ClaudeFlowMonitoring";
import { ClaudeFlowMemory } from "./ClaudeFlowMemory";
import { ClaudeFlowWorkflow } from "./ClaudeFlowWorkflow";
import { 
  ClaudeFlowIntegration, 
  ClaudeFlowAgent, 
  ClaudeFlowTask, 
  ClaudeFlowSystemStatus,
  DEFAULT_CLAUDE_FLOW_CONFIG,
  CLAUDE_FLOW_MCP_CONFIG 
} from "@/lib/claudeFlowIntegration";
import { createMCPClient } from "@/lib/mcpClient";
import { useOptimizedMCP } from "@/contexts/OptimizedMCPContext";
import { 
  useClaudeFlowWebSocket, 
  useClaudeFlowAgentUpdates, 
  useClaudeFlowTaskUpdates,
  useClaudeFlowSystemStatus 
} from "@/hooks/useClaudeFlowWebSocket";

interface ClaudeFlowManagerProps {
  onBack: () => void;
  className?: string;
}

// Agent Templates
interface AgentTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  type: string;
  systemPrompt: string;
  capabilities: string[];
  priority: number;
  maxConcurrentTasks: number;
  color: string;
}

const AGENT_TEMPLATES: AgentTemplate[] = [
  {
    id: 'researcher',
    name: 'Research Specialist',
    description: 'Expert at gathering information, analyzing data, and providing comprehensive research reports',
    icon: <Search className="h-4 w-4" />,
    type: 'researcher',
    systemPrompt: 'You are a research specialist focused on gathering accurate information, analyzing data patterns, and providing well-structured research reports. Always cite sources and provide evidence-based conclusions.',
    capabilities: ['web-search', 'data-analysis', 'fact-checking', 'report-generation'],
    priority: 7,
    maxConcurrentTasks: 2,
    color: 'text-blue-500'
  },
  {
    id: 'implementer',
    name: 'Code Implementer',
    description: 'Specialized in writing clean, efficient code and implementing features according to specifications',
    icon: <Code className="h-4 w-4" />,
    type: 'implementer',
    systemPrompt: 'You are a senior software developer focused on writing clean, efficient, and maintainable code. Follow best practices, write comprehensive tests, and ensure code quality.',
    capabilities: ['coding', 'testing', 'debugging', 'code-review'],
    priority: 8,
    maxConcurrentTasks: 3,
    color: 'text-green-500'
  },
  {
    id: 'reviewer',
    name: 'Quality Reviewer',
    description: 'Focused on code review, quality assurance, and maintaining high standards',
    icon: <FileText className="h-4 w-4" />,
    type: 'reviewer',
    systemPrompt: 'You are a quality assurance specialist focused on reviewing code, documentation, and deliverables for quality, consistency, and best practices.',
    capabilities: ['code-review', 'quality-assurance', 'documentation-review', 'standards-compliance'],
    priority: 6,
    maxConcurrentTasks: 4,
    color: 'text-yellow-500'
  },
  {
    id: 'security',
    name: 'Security Analyst',
    description: 'Expert in identifying security vulnerabilities and implementing security best practices',
    icon: <Shield className="h-4 w-4" />,
    type: 'security',
    systemPrompt: 'You are a cybersecurity specialist focused on identifying vulnerabilities, implementing security measures, and ensuring compliance with security best practices.',
    capabilities: ['vulnerability-scanning', 'security-analysis', 'compliance-checking', 'threat-modeling'],
    priority: 9,
    maxConcurrentTasks: 2,
    color: 'text-red-500'
  },
  {
    id: 'data-analyst',
    name: 'Data Analyst',
    description: 'Specialized in data processing, visualization, and generating insights from complex datasets',
    icon: <Database className="h-4 w-4" />,
    type: 'researcher',
    systemPrompt: 'You are a data analyst expert at processing large datasets, creating visualizations, and extracting actionable insights from data.',
    capabilities: ['data-processing', 'visualization', 'statistical-analysis', 'reporting'],
    priority: 7,
    maxConcurrentTasks: 2,
    color: 'text-purple-500'
  },
  {
    id: 'tester',
    name: 'Test Engineer',
    description: 'Focused on creating comprehensive test suites and ensuring software quality through testing',
    icon: <TestTube className="h-4 w-4" />,
    type: 'implementer',
    systemPrompt: 'You are a test engineer specialized in creating comprehensive test suites, automated testing, and ensuring software quality through rigorous testing practices.',
    capabilities: ['test-automation', 'unit-testing', 'integration-testing', 'performance-testing'],
    priority: 6,
    maxConcurrentTasks: 3,
    color: 'text-cyan-500'
  }
];

// Task Templates
interface TaskTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  type: string;
  taskDescription: string;
  priority: number;
  assignToAgentType: string;
  timeout: number;
  color: string;
  preferredAgentType?: string;
  estimatedDuration?: number;
}

const TASK_TEMPLATES: TaskTemplate[] = [
  {
    id: 'research-task',
    name: 'Research & Analysis',
    description: 'Comprehensive research on a specific topic with detailed analysis',
    icon: <Search className="h-4 w-4" />,
    type: 'research',
    taskDescription: 'Research [TOPIC] and provide a comprehensive analysis including:\n• Key findings and insights\n• Data sources and methodology\n• Conclusions and recommendations\n• Executive summary',
    priority: 7,
    assignToAgentType: 'researcher',
    timeout: 3600,
    color: 'text-blue-500'
  },
  {
    id: 'code-implementation',
    name: 'Feature Implementation',
    description: 'Implement a new feature or functionality according to specifications',
    icon: <Code className="h-4 w-4" />,
    type: 'implementation',
    taskDescription: 'Implement [FEATURE_NAME] with the following requirements:\n• [REQUIREMENT_1]\n• [REQUIREMENT_2]\n• Include comprehensive tests\n• Follow coding standards\n• Update documentation',
    priority: 8,
    assignToAgentType: 'implementer',
    timeout: 7200,
    color: 'text-green-500'
  },
  {
    id: 'bug-fix',
    name: 'Bug Investigation & Fix',
    description: 'Investigate and fix a reported bug with proper testing',
    icon: <Bug className="h-4 w-4" />,
    type: 'implementation',
    taskDescription: 'Investigate and fix bug: [BUG_DESCRIPTION]\n• Reproduce the issue\n• Identify root cause\n• Implement fix\n• Add regression test\n• Verify solution',
    priority: 9,
    assignToAgentType: 'implementer',
    timeout: 5400,
    color: 'text-red-500'
  },
  {
    id: 'code-review',
    name: 'Code Review',
    description: 'Review code changes for quality, standards, and best practices',
    icon: <FileText className="h-4 w-4" />,
    type: 'review',
    taskDescription: 'Review the following code changes:\n• Check for code quality and standards\n• Verify test coverage\n• Ensure security best practices\n• Validate documentation\n• Provide constructive feedback',
    priority: 6,
    assignToAgentType: 'reviewer',
    timeout: 1800,
    color: 'text-yellow-500'
  },
  {
    id: 'security-audit',
    name: 'Security Audit',
    description: 'Comprehensive security review of code or system components',
    icon: <Shield className="h-4 w-4" />,
    type: 'analysis',
    taskDescription: 'Perform security audit on [COMPONENT/SYSTEM]:\n• Identify potential vulnerabilities\n• Check for security best practices\n• Review access controls\n• Validate input sanitization\n• Generate security report',
    priority: 9,
    assignToAgentType: 'security',
    timeout: 5400,
    color: 'text-red-500'
  },
  {
    id: 'documentation',
    name: 'Documentation Creation',
    description: 'Create comprehensive documentation for features or systems',
    icon: <BookOpen className="h-4 w-4" />,
    type: 'documentation',
    taskDescription: 'Create documentation for [FEATURE/SYSTEM]:\n• User guide with examples\n• API documentation\n• Installation instructions\n• Troubleshooting guide\n• Best practices',
    priority: 5,
    assignToAgentType: 'reviewer',
    timeout: 3600,
    color: 'text-indigo-500'
  },
  {
    id: 'performance-analysis',
    name: 'Performance Analysis',
    description: 'Analyze system performance and provide optimization recommendations',
    icon: <TrendingUp className="h-4 w-4" />,
    type: 'analysis',
    taskDescription: 'Analyze performance of [SYSTEM/COMPONENT]:\n• Identify bottlenecks\n• Measure key metrics\n• Compare against benchmarks\n• Provide optimization recommendations\n• Create performance report',
    priority: 7,
    assignToAgentType: 'researcher',
    timeout: 4800,
    color: 'text-orange-500'
  },
  {
    id: 'testing-suite',
    name: 'Test Suite Creation',
    description: 'Create comprehensive test suite for features or components',
    icon: <TestTube className="h-4 w-4" />,
    type: 'testing',
    taskDescription: 'Create comprehensive test suite for [FEATURE/COMPONENT]:\n• Unit tests with high coverage\n• Integration tests\n• Edge case testing\n• Performance tests\n• Test documentation',
    priority: 7,
    assignToAgentType: 'implementer',
    timeout: 5400,
    color: 'text-cyan-500'
  }
];

// Create search engine instance
const searchEngine = SearchEngine.getInstance();

/**
 * Claude-Flow Manager Component
 * Provides a comprehensive UI for managing Claude-Flow agent orchestration
 */
const ClaudeFlowManagerInner: React.FC<ClaudeFlowManagerProps> = ({
  onBack,
  className
}) => {
  // MCP Context
  const { servers, loadServers } = useOptimizedMCP();
  
  // Error handling
  const { addError, handleAsync, clearErrors } = useClaudeFlowError({
    onError: (error) => {
      showToast(error.message, 'error');
    },
    onRecovery: () => {
      showToast('Successfully recovered from error', 'success');
    }
  });
  
  // State management
  const [integration] = useState(() => new ClaudeFlowIntegration(DEFAULT_CLAUDE_FLOW_CONFIG));
  const [agents, setAgents] = useState<ClaudeFlowAgent[]>([]);
  const [tasks, setTasks] = useState<ClaudeFlowTask[]>([]);
  const [systemStatus, setSystemStatus] = useState<ClaudeFlowSystemStatus | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [toast, setToast] = useState<{ message: string; type: "success" | "error" } | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [showSpawnAgentDialog, setShowSpawnAgentDialog] = useState(false);
  const [showCreateTaskDialog, setShowCreateTaskDialog] = useState(false);
  const [spawnAgentLoading, setSpawnAgentLoading] = useState(false);
  const [createTaskLoading, setCreateTaskLoading] = useState(false);
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const [showBulkOperationDialog, setShowBulkOperationDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  
  // Export/Import engine
  const exportImportEngine = ExportImportEngine.getInstance();
  
  // Bulk operations
  const bulkOperations = useBulkOperations({
    integration: isInitialized ? integration : null,
    onOperationComplete: (result) => {
      showToast(
        `Bulk operation completed: ${result.successful.length} successful, ${result.failed.length} failed`,
        result.failed.length > 0 ? 'error' : 'success'
      );
      // Reload data after bulk operation
      loadData();
    },
    onOperationError: (error) => {
      showToast(`Bulk operation failed: ${error.message}`, 'error');
    }
  });
  
  // Search functionality
  const {
    isSearching,
    results: searchResults,
    filters,
    searchHistory,
    savedFilters,
    search,
    updateFilters,
    clearFilters,
    saveFilter,
    loadSavedFilter,
    deleteSavedFilter,
    indexAgent,
    indexTask,
    getFilterOptions
  } = useSearch();
  const [showKeyboardShortcutsHelp, setShowKeyboardShortcutsHelp] = useState(false);
  const [, setIsFullscreen] = useState(false);
  
  // Refs
  const toastTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const refreshIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Define keyboard shortcut actions
  const keyboardActions: ClaudeFlowKeyboardActions = {
    // Navigation shortcuts
    switchToOverview: () => setActiveTab('overview'),
    switchToAgents: () => setActiveTab('agents'),
    switchToTasks: () => setActiveTab('tasks'),
    switchToWorkflows: () => setActiveTab('workflows'),
    switchToMemory: () => setActiveTab('memory'),
    switchToMonitoring: () => setActiveTab('monitoring'),
    switchToTerminal: () => setActiveTab('terminal'),
    
    // Agent shortcuts
    spawnAgent: () => {
      if (isInitialized) {
        setShowSpawnAgentDialog(true);
      } else {
        showToast('Claude Flow not initialized', 'error');
      }
    },
    refreshAgents: async () => {
      if (isInitialized) {
        await loadData();
        showToast('Agents refreshed', 'success');
      }
    },
    
    // Task shortcuts
    createTask: () => {
      if (isInitialized) {
        setShowCreateTaskDialog(true);
      } else {
        showToast('Claude Flow not initialized', 'error');
      }
    },
    refreshTasks: async () => {
      if (isInitialized) {
        await loadData();
        showToast('Tasks refreshed', 'success');
      }
    },
    
    // General shortcuts
    refreshAll: async () => {
      await loadData();
      showToast('All data refreshed', 'success');
    },
    showHelp: () => setShowKeyboardShortcutsHelp(true),
    focusSearch: () => {
      // Try to focus the search input if it exists
      const searchInput = document.querySelector('input[type="search"], input[placeholder*="search" i], input[placeholder*="Search" i]') as HTMLInputElement;
      if (searchInput) {
        searchInput.focus();
        searchInput.select();
      } else {
        showToast('Search input not found', 'error');
      }
    },
    toggleFullscreen: () => {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen().then(() => {
          setIsFullscreen(true);
          showToast('Entered fullscreen mode', 'success');
        }).catch((err) => {
          console.error('Failed to enter fullscreen:', err);
          showToast('Failed to enter fullscreen', 'error');
        });
      } else {
        document.exitFullscreen().then(() => {
          setIsFullscreen(false);
          showToast('Exited fullscreen mode', 'success');
        }).catch((err) => {
          console.error('Failed to exit fullscreen:', err);
          showToast('Failed to exit fullscreen', 'error');
        });
      }
    }
  };

  // Keyboard shortcuts integration
  const { 
    shortcuts, 
    formatShortcut
  } = useClaudeFlowKeyboardShortcuts({
    enabled: !loading && !showSpawnAgentDialog && !showCreateTaskDialog && !showKeyboardShortcutsHelp,
    actions: keyboardActions,
    onShortcutTriggered: (shortcut) => {
      console.log('Shortcut triggered:', shortcut.description);
    }
  });

  // WebSocket integration
  const { state: wsState, actions: wsActions } = useClaudeFlowWebSocket({
    host: DEFAULT_CLAUDE_FLOW_CONFIG.host,
    port: DEFAULT_CLAUDE_FLOW_CONFIG.port,
    autoConnect: false, // We'll connect after initialization
    onConnect: () => {
      console.log('Claude Flow WebSocket connected');
      showToast('Real-time updates connected', 'success');
    },
    onDisconnect: (reason) => {
      console.log('Claude Flow WebSocket disconnected:', reason);
      showToast('Real-time updates disconnected', 'error');
    },
    onError: (error) => {
      console.error('Claude Flow WebSocket error:', error);
    }
  });

  // Real-time agent updates
  useClaudeFlowAgentUpdates((agent, eventType) => {
    console.log('Agent update:', eventType, agent);
    
    switch (eventType) {
      case 'agent.spawned':
        setAgents(prev => [...prev, agent]);
        indexAgent(agent);
        break;
      case 'agent.updated':
        setAgents(prev => prev.map(a => a.id === agent.id ? agent : a));
        indexAgent(agent);
        break;
      case 'agent.terminated':
        const terminatedAgent = { ...agent, status: 'terminated' as const };
        setAgents(prev => prev.map(a => a.id === agent.id ? terminatedAgent : a));
        indexAgent(terminatedAgent);
        break;
    }
  });

  // Real-time task updates
  useClaudeFlowTaskUpdates((task, eventType) => {
    console.log('Task update:', eventType, task);
    
    switch (eventType) {
      case 'task.created':
        setTasks(prev => [...prev, task]);
        indexTask(task);
        break;
      case 'task.assigned':
      case 'task.updated':
      case 'task.completed':
      case 'task.failed':
      case 'task.cancelled':
        setTasks(prev => prev.map(t => t.id === task.id ? task : t));
        indexTask(task);
        break;
    }
  });

  // Real-time system status updates
  useClaudeFlowSystemStatus((status) => {
    console.log('System status update:', status);
    setSystemStatus(status);
  });

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (toastTimeoutRef.current) {
        clearTimeout(toastTimeoutRef.current);
      }
      if (refreshIntervalRef.current) {
        clearInterval(refreshIntervalRef.current);
      }
      wsActions.disconnect();
    };
  }, [wsActions]);

  // Initialize integration and load data
  useEffect(() => {
    const initializeIntegration = async () => {
      try {
        setLoading(true);
        setError(null);
        clearErrors();
        
        // Check if Claude Flow server is configured in MCP
        const claudeFlowServer = servers.find(s => s.name === 'claude-flow');
        
        if (!claudeFlowServer) {
          console.log('Claude Flow server not found in MCP, using demo mode');
          const error = 'Claude Flow server not available. Running in demo mode with mock data.';
          setError(error);
          setLoading(false);
          // Don't return - continue with mock data
        }
        
        if (claudeFlowServer) {
          // Create MCP client for Claude Flow
          const mcpClient = createMCPClient('claude-flow');
          
          // Initialize the integration with error handling
          await handleAsync(
            () => integration.initialize(mcpClient),
            'connection',
            'Failed to initialize Claude Flow integration'
          );
          setIsInitialized(true);
          
          // Load initial data
          await loadData();
          
          // Connect WebSocket for real-time updates
          try {
            await wsActions.connect();
          } catch (wsError) {
            console.error('Failed to connect WebSocket:', wsError);
            addError('connection', 'WebSocket connection failed', wsError, true);
            // Continue without WebSocket - we'll still have periodic refresh
          }
        } else {
          // Use mock data when Claude Flow is not available
          setIsInitialized(false);
          await loadData();
        }
        
        // Set up periodic refresh as fallback
        refreshIntervalRef.current = setInterval(loadData, 30000); // Refresh every 30 seconds as fallback
        
      } catch (err) {
        console.error('Failed to initialize Claude-Flow integration:', err);
        const errorMessage = 'Failed to connect to Claude-Flow. Please ensure Claude-Flow is running and accessible.';
        setError(errorMessage);
        addError('connection', errorMessage, err, true);
      } finally {
        setLoading(false);
      }
    };

    initializeIntegration();
  }, [servers]);
  
  // Re-load data when initialization status changes
  useEffect(() => {
    if (isInitialized) {
      loadData();
    }
  }, [isInitialized]);

  /**
   * Load all data from Claude-Flow
   */
  const loadData = async () => {
    try {
      if (isInitialized) {
        // Load real data from Claude Flow with error handling
        const [agentList, taskList, status] = await Promise.all([
          handleAsync(() => integration.listAgents(), 'general', 'Failed to load agents'),
          handleAsync(() => integration.listTasks({ limit: 50 }), 'general', 'Failed to load tasks'),
          handleAsync(() => integration.getSystemStatus(), 'general', 'Failed to load system status')
        ]);
        
        if (agentList) {
          setAgents(agentList);
          // Index agents for search
          agentList.forEach(agent => searchEngine.indexAgent(agent));
        }
        if (taskList) {
          setTasks(taskList);
          // Index tasks for search
          taskList.forEach(task => searchEngine.indexTask(task));
        }
        if (status) setSystemStatus(status);
      } else {
        // Use mock data if not initialized
        const mockAgents: ClaudeFlowAgent[] = [
        {
          id: 'agent_1',
          name: 'Research Assistant',
          type: 'researcher',
          status: 'running',
          capabilities: ['web-search', 'analysis', 'reporting'],
          systemPrompt: 'You are a research assistant specialized in gathering and analyzing information.',
          maxConcurrentTasks: 3,
          priority: 8,
          createdAt: new Date().toISOString(),
          lastActivity: new Date().toISOString()
        },
        {
          id: 'agent_2',
          name: 'Code Developer',
          type: 'implementer',
          status: 'idle',
          capabilities: ['coding', 'debugging', 'testing'],
          systemPrompt: 'You are a software developer focused on writing clean, efficient code.',
          maxConcurrentTasks: 2,
          priority: 7,
          createdAt: new Date().toISOString()
        }
      ];

      const mockTasks: ClaudeFlowTask[] = [
        {
          id: 'task_1',
          type: 'research',
          description: 'Analyze latest AI development trends',
          status: 'running',
          priority: 8,
          agentId: 'agent_1',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'task_2',
          type: 'implementation',
          description: 'Implement user authentication system',
          status: 'pending',
          priority: 7,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      const mockStatus: ClaudeFlowSystemStatus = {
        orchestrator: {
          running: true,
          uptime: 3600,
          totalAgents: 2,
          activeAgents: 1,
          totalTasks: 2,
          completedTasks: 5
        },
        memory: {
          totalEntries: 150,
          memoryUsage: 0.25
        },
        performance: {
          avgResponseTime: 1200,
          throughput: 15.5,
          errorRate: 0.02
        }
      };

        setAgents(mockAgents);
        setTasks(mockTasks);
        setSystemStatus(mockStatus);
        
        // Index mock data for search
        mockAgents.forEach(agent => searchEngine.indexAgent(agent));
        mockTasks.forEach(task => searchEngine.indexTask(task));
      }
      
    } catch (err) {
      console.error('Failed to load Claude-Flow data:', err);
      showToast('Failed to load data from Claude-Flow', 'error');
    }
  };

  /**
   * Show toast notification
   */
  const showToast = (message: string, type: "success" | "error") => {
    if (toastTimeoutRef.current) {
      clearTimeout(toastTimeoutRef.current);
    }
    
    setToast({ message, type });
    
    toastTimeoutRef.current = setTimeout(() => {
      setToast(null);
      toastTimeoutRef.current = null;
    }, 3000);
  };

  /**
   * Format uptime in human readable format
   */
  const formatUptime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}m`;
  };

  /**
   * Get status badge variant
   */
  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'running': return 'success';
      case 'idle': return 'secondary';
      case 'paused': return 'warning';
      case 'error': return 'destructive';
      case 'terminated': return 'secondary';
      default: return 'secondary';
    }
  };

  /**
   * Get status icon
   */
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <Play className="h-3 w-3" />;
      case 'idle': return <Pause className="h-3 w-3" />;
      case 'error': return <XCircle className="h-3 w-3" />;
      case 'terminated': return <Square className="h-3 w-3" />;
      default: return <Clock className="h-3 w-3" />;
    }
  };

  /**
   * Get agent type icon
   */
  /**
   * Spawn a new agent
   */
  const handleSpawnAgent = async (agentConfig: any) => {
    if (!isInitialized) {
      showToast('Claude Flow not initialized', 'error');
      return;
    }

    setSpawnAgentLoading(true);
    try {
      const newAgent = await handleAsync(
        () => integration.spawnAgent(agentConfig),
        'agent',
        'Failed to spawn agent'
      );
      
      if (newAgent) {
        showToast(`Agent "${newAgent.name}" spawned successfully!`, 'success');
        setShowSpawnAgentDialog(false);
        // Reload agents list
        await loadData();
      }
    } catch (err) {
      // Error already handled by handleAsync
      console.error('Failed to spawn agent:', err);
    } finally {
      setSpawnAgentLoading(false);
    }
  };

  /**
   * Create a new task
   */
  const handleCreateTask = async (taskConfig: any) => {
    if (!isInitialized) {
      showToast('Claude Flow not initialized', 'error');
      return;
    }

    setCreateTaskLoading(true);
    try {
      const newTask = await handleAsync(
        () => integration.createTask(taskConfig),
        'task',
        'Failed to create task'
      );
      
      if (newTask) {
        showToast(`Task created successfully!`, 'success');
        setShowCreateTaskDialog(false);
        // Reload tasks list
        await loadData();
      }
    } catch (err) {
      // Error already handled by handleAsync
      console.error('Failed to create task:', err);
    } finally {
      setCreateTaskLoading(false);
    }
  };

  /**
   * Terminate an agent
   */
  const handleTerminateAgent = async (agentId: string, agentName: string) => {
    if (!isInitialized) {
      showToast('Claude Flow not initialized', 'error');
      return;
    }

    try {
      await handleAsync(
        () => integration.terminateAgent(agentId, 'User requested termination'),
        'agent',
        `Failed to terminate agent "${agentName}"`
      );
      
      showToast(`Agent "${agentName}" terminated`, 'success');
      // Reload agents list
      await loadData();
    } catch (err) {
      // Error already handled by handleAsync
      console.error('Failed to terminate agent:', err);
    }
  };

  /**
   * Handle bulk operation for agents or tasks
   */
  const handleBulkAction = (action: string, parameters?: Record<string, any>) => {
    const entityType = activeTab === 'agents' ? 'agent' : 'task';
    bulkOperations.prepareBulkOperation(entityType, action, parameters);
    setShowBulkOperationDialog(true);
  };

  /**
   * Confirm bulk operation
   */
  const confirmBulkOperation = () => {
    bulkOperations.executeBulkOperation();
  };

  /**
   * Cancel bulk operation dialog
   */
  const cancelBulkOperation = () => {
    setShowBulkOperationDialog(false);
    bulkOperations.cancelOperation();
  };

  /**
   * Cancel a task
   */
  const handleCancelTask = async (taskId: string) => {
    if (!isInitialized) {
      showToast('Claude Flow not initialized', 'error');
      return;
    }

    try {
      await handleAsync(
        () => integration.cancelTask(taskId, 'User requested cancellation'),
        'task',
        'Failed to cancel task'
      );
      
      showToast('Task cancelled', 'success');
      // Reload tasks list
      await loadData();
    } catch (err) {
      // Error already handled by handleAsync
      console.error('Failed to cancel task:', err);
    }
  };

  /**
   * Handle export
   */
  const handleExport = async (options: ExportOptions) => {
    setIsExporting(true);
    try {
      // Gather data based on options
      const data: any = {};
      
      if (options.includeSettings) {
        // Get settings from localStorage or MCP
        const settings = localStorage.getItem('claudeFlowSettings');
        if (settings) {
          data.settings = JSON.parse(settings);
        }
      }
      
      if (options.includeAgents) {
        data.agents = agents;
      }
      
      if (options.includeTasks) {
        data.tasks = tasks;
      }
      
      if (options.includeMemory && isInitialized) {
        // Get memory data from integration
        try {
          const memory = await integration.getMemorySnapshots();
          data.memory = memory;
        } catch (err) {
          console.error('Failed to get memory data:', err);
        }
      }
      
      if (options.includeMetrics && systemStatus) {
        data.metrics = {
          systemStatus,
          timestamp: new Date().toISOString()
        };
      }
      
      // Export using the engine
      await exportImportEngine.exportData(data, options);
      
      showToast('Export completed successfully', 'success');
      setShowExportDialog(false);
    } catch (err) {
      console.error('Export failed:', err);
      showToast('Export failed: ' + (err instanceof Error ? err.message : 'Unknown error'), 'error');
    } finally {
      setIsExporting(false);
    }
  };

  /**
   * Handle import
   */
  const handleImport = async (file: File, options: ImportOptions): Promise<ImportResult> => {
    try {
      const result = await exportImportEngine.importData(file, options, {
        onProgress: (progress) => {
          console.log('Import progress:', progress);
        },
        onConflict: async (conflict) => {
          // For now, use the merge strategy from options
          return options.mergeConflicts;
        }
      });
      
      if (result.success) {
        showToast('Import completed successfully', 'success');
        // Reload data to reflect imported changes
        await loadData();
      } else {
        showToast('Import completed with errors', 'error');
      }
      
      return result;
    } catch (err) {
      console.error('Import failed:', err);
      showToast('Import failed: ' + (err instanceof Error ? err.message : 'Unknown error'), 'error');
      throw err;
    }
  };

  if (loading) {
    return (
      <div className="flex flex-col h-screen max-h-screen bg-background text-foreground">
        <div className="flex-1 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3 }}
            className="flex flex-col items-center gap-4 p-8 rounded-xl bg-card/50 backdrop-blur-sm border border-border/20"
          >
            <div className="p-3 rounded-xl bg-gradient-to-br from-purple-500/20 to-blue-500/20 backdrop-blur-sm">
              <Loader2 className="h-8 w-8 animate-spin text-purple-500" />
            </div>
            <p className="text-sm text-muted-foreground font-medium">Connecting to Claude-Flow...</p>
          </motion.div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-screen max-h-screen bg-background text-foreground ${className || ""}`}>
      <div className="max-w-7xl mx-auto w-full flex flex-col h-full">
        {/* Header */}
        <motion.header
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="flex-shrink-0 relative z-10"
        >
          <div className="absolute inset-0 bg-background/80 backdrop-blur-xl border-b border-white/5" />
          
          <div className="relative px-4 sm:px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 min-w-0 flex-1">
                <Button
                  variant="glass"
                  size="icon"
                  onClick={onBack}
                  className="h-10 w-10 flex-shrink-0 hover:scale-105 transition-transform"
                >
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                
                <div className="flex items-center gap-3 min-w-0 flex-1">
                  <div className="p-2.5 rounded-xl bg-gradient-to-br from-purple-500/20 to-blue-500/20 backdrop-blur-sm border border-purple-500/20 shadow-lg">
                    <Bot className="h-6 w-6 text-purple-500" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <h1 className="text-xl font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                      Claude-Flow Orchestration
                    </h1>
                    <div className="text-sm text-muted-foreground truncate">
                      Advanced AI agent management & coordination
                      {systemStatus && (
                        <span className="ml-2 inline-flex items-center gap-2">
                          <span className="inline-flex items-center gap-1">
                            <span className={`h-2 w-2 rounded-full animate-pulse inline-block ${
                              systemStatus.orchestrator.running ? 'bg-green-500' : 'bg-red-500'
                            }`} />
                            {systemStatus.orchestrator.running ? 'Online' : 'Offline'}
                          </span>
                          {wsState.connected && (
                            <span className="inline-flex items-center gap-1 text-xs">
                              <Network className="h-3 w-3 text-green-500" />
                              <span className="text-green-500">Live</span>
                            </span>
                          )}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              
              {/* System Stats */}
              {systemStatus && (
                <div className="hidden lg:flex items-center gap-3">
                  <div className="px-3 py-1.5 rounded-lg bg-card/50 backdrop-blur-sm border border-border/20">
                    <div className="text-xs text-muted-foreground">Agents</div>
                    <div className="text-lg font-bold text-purple-500">
                      {systemStatus.orchestrator.activeAgents}/{systemStatus.orchestrator.totalAgents}
                    </div>
                  </div>
                  <div className="px-3 py-1.5 rounded-lg bg-blue-500/10 backdrop-blur-sm border border-blue-500/20">
                    <div className="text-xs text-blue-600 dark:text-blue-400">Tasks</div>
                    <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                      {systemStatus.orchestrator.totalTasks}
                    </div>
                  </div>
                  <div className="px-3 py-1.5 rounded-lg bg-green-500/10 backdrop-blur-sm border border-green-500/20">
                    <div className="text-xs text-green-600 dark:text-green-400">Uptime</div>
                    <div className="text-sm font-bold text-green-600 dark:text-green-400">
                      {formatUptime(systemStatus.orchestrator.uptime)}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </motion.header>

        {/* Error Display */}
        <AnimatePresence>
          {error && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="flex-shrink-0 mx-4 sm:mx-6 mb-2 p-3 rounded-lg bg-destructive/10 border border-destructive/20 flex items-start gap-2 text-sm text-destructive"
            >
              <AlertCircle className="h-4 w-4 flex-shrink-0 mt-0.5" />
              <div className="flex-1">
                <span>{error}</span>
                {error.includes('not configured') && (
                  <div className="mt-2 flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={async () => {
                        await loadServers();
                        // Retry initialization after reloading servers
                        window.location.reload();
                      }}
                      className="h-7 px-3 text-xs border-destructive/50 hover:bg-destructive/20"
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Reload MCP Servers
                    </Button>
                    <span className="text-xs text-muted-foreground">or</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        // Navigate to MCP settings to add Claude Flow
                        window.dispatchEvent(new CustomEvent('navigate-to-mcp-add', { 
                          detail: { 
                            serverConfig: CLAUDE_FLOW_MCP_CONFIG 
                          } 
                        }));
                      }}
                      className="h-7 px-3 text-xs border-destructive/50 hover:bg-destructive/20"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Configure Manually
                    </Button>
                  </div>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={loadData}
                className="h-6 px-2 text-xs hover:bg-destructive/20"
              >
                <RefreshCw className="h-3 w-3" />
              </Button>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Content */}
        <main className="flex-1 overflow-hidden grid grid-rows-[auto_1fr] gap-6 px-4 sm:px-6 pb-6">
          {/* Tab Navigation */}
          <nav className="sticky top-0 bg-background/95 backdrop-blur-sm z-10 relative">
            <Tabs value={activeTab} onValueChange={(tab) => {
              setActiveTab(tab);
              // Update search entity type based on tab
              if (tab === 'agents') {
                updateFilters({ entityType: 'agents' });
              } else if (tab === 'tasks') {
                updateFilters({ entityType: 'tasks' });
              } else {
                updateFilters({ entityType: 'all' });
              }
            }}>
              <TabsList className="grid w-full max-w-4xl mx-auto grid-cols-7 h-12 p-1 bg-white/5 border border-white/10 backdrop-blur-xl">
                <TabsTrigger value="overview" className="gap-2 text-sm font-medium">
                  <Monitor className="h-4 w-4 text-purple-500" />
                  <span className="hidden sm:inline">Overview</span>
                </TabsTrigger>
                <TabsTrigger value="agents" className="gap-2 text-sm font-medium">
                  <Bot className="h-4 w-4 text-blue-500" />
                  <span className="hidden sm:inline">Agents</span>
                </TabsTrigger>
                <TabsTrigger value="tasks" className="gap-2 text-sm font-medium">
                  <Zap className="h-4 w-4 text-green-500" />
                  <span className="hidden sm:inline">Tasks</span>
                </TabsTrigger>
                <TabsTrigger value="workflows" className="gap-2 text-sm font-medium">
                  <GitBranch className="h-4 w-4 text-orange-500" />
                  <span className="hidden sm:inline">Workflow</span>
                </TabsTrigger>
                <TabsTrigger value="memory" className="gap-2 text-sm font-medium">
                  <Brain className="h-4 w-4 text-orange-500" />
                  <span className="hidden sm:inline">Memory</span>
                </TabsTrigger>
                <TabsTrigger value="monitoring" className="gap-2 text-sm font-medium">
                  <Activity className="h-4 w-4 text-cyan-500" />
                  <span className="hidden sm:inline">Monitor</span>
                </TabsTrigger>
                <TabsTrigger value="terminal" className="gap-2 text-sm font-medium">
                  <Terminal className="h-4 w-4 text-pink-500" />
                  <span className="hidden sm:inline">Terminal</span>
                </TabsTrigger>
              </TabsList>
              
              {/* Keyboard Help Button */}
              <div className="absolute top-2 right-2">
                <ClaudeFlowActionWithShortcut
                  shortcut="Ctrl + H"
                  description="Keyboard shortcuts help"
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowKeyboardShortcutsHelp(true)}
                    className="h-8 w-8 p-0 hover:bg-white/10 transition-colors"
                    title="Keyboard shortcuts help (Ctrl + H)"
                  >
                    <Keyboard className="h-4 w-4" />
                  </Button>
                </ClaudeFlowActionWithShortcut>
              </div>
              
              {/* Tab Content */}
              <div className="mt-6 overflow-hidden">
                {/* Overview Panel */}
                <TabsContent value="overview" className="m-0">
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* System Status Card */}
                    <Card className="lg:col-span-2 border-purple-500/20 bg-gradient-to-br from-purple-500/5 to-blue-500/5">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Activity className="h-5 w-5 text-purple-500" />
                          System Status
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        {systemStatus ? (
                          <div className="grid grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Orchestrator</span>
                                <Badge variant={systemStatus.orchestrator.running ? "success" : "destructive"}>
                                  {systemStatus.orchestrator.running ? "Running" : "Stopped"}
                                </Badge>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Total Agents</span>
                                <span className="font-medium">{systemStatus.orchestrator.totalAgents}</span>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Active Agents</span>
                                <span className="font-medium text-green-500">{systemStatus.orchestrator.activeAgents}</span>
                              </div>
                            </div>
                            <div className="space-y-2">
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Total Tasks</span>
                                <span className="font-medium">{systemStatus.orchestrator.totalTasks}</span>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Completed</span>
                                <span className="font-medium text-blue-500">{systemStatus.orchestrator.completedTasks}</span>
                              </div>
                              <div className="flex items-center justify-between">
                                <span className="text-sm text-muted-foreground">Error Rate</span>
                                <span className="font-medium text-orange-500">{(systemStatus.performance.errorRate * 100).toFixed(1)}%</span>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center justify-center py-8">
                            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                          </div>
                        )}
                      </CardContent>
                    </Card>

                    {/* Performance Metrics */}
                    <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-green-500/5">
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Zap className="h-5 w-5 text-blue-500" />
                          Performance
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        {systemStatus ? (
                          <div className="space-y-4">
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm text-muted-foreground">Avg Response</span>
                                <span className="text-sm font-medium">{systemStatus.performance.avgResponseTime}ms</span>
                              </div>
                              <div className="w-full bg-secondary rounded-full h-2">
                                <div 
                                  className="bg-blue-500 h-2 rounded-full transition-all duration-300" 
                                  style={{ width: `${Math.min(systemStatus.performance.avgResponseTime / 2000 * 100, 100)}%` }}
                                />
                              </div>
                            </div>
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm text-muted-foreground">Throughput</span>
                                <span className="text-sm font-medium">{systemStatus.performance.throughput.toFixed(1)}/sec</span>
                              </div>
                              <div className="w-full bg-secondary rounded-full h-2">
                                <div 
                                  className="bg-green-500 h-2 rounded-full transition-all duration-300" 
                                  style={{ width: `${Math.min(systemStatus.performance.throughput / 50 * 100, 100)}%` }}
                                />
                              </div>
                            </div>
                            <div>
                              <div className="flex items-center justify-between mb-1">
                                <span className="text-sm text-muted-foreground">Memory Usage</span>
                                <span className="text-sm font-medium">{(systemStatus.memory.memoryUsage * 100).toFixed(1)}%</span>
                              </div>
                              <div className="w-full bg-secondary rounded-full h-2">
                                <div 
                                  className="bg-orange-500 h-2 rounded-full transition-all duration-300" 
                                  style={{ width: `${systemStatus.memory.memoryUsage * 100}%` }}
                                />
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center justify-center py-8">
                            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Agents Panel */}
                <TabsContent value="agents" className="m-0">
                  <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    {/* Main Agents Card */}
                    <div className="lg:col-span-3">
                      <Card className="border-blue-500/20 bg-gradient-to-br from-blue-500/5 to-purple-500/5">
                        <CardHeader>
                          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                            <CardTitle className="flex items-center gap-2">
                              <Bot className="h-5 w-5 text-blue-500" />
                              Active Agents ({agents.length})
                            </CardTitle>
                            <div className="flex items-center gap-2 w-full sm:w-auto">
                              <SearchBar
                                value={filters.query}
                                onChange={(query) => updateFilters({ query })}
                                onSearch={() => search(filters)}
                                onClear={() => updateFilters({ query: '' })}
                                searchHistory={searchHistory}
                                isSearching={false}
                                onSaveFilter={() => {
                                  const name = prompt('Save filter as:');
                                  if (name) saveFilter(name);
                                }}
                                placeholder="Search agents..."
                                className="flex-1 sm:w-64"
                              />
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => setShowFilterPanel(!showFilterPanel)}
                                className={showFilterPanel ? 'bg-accent' : ''}
                              >
                                <Filter className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => setShowExportDialog(true)}
                                title="Export data"
                              >
                                <Download className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => setShowImportDialog(true)}
                                title="Import data"
                              >
                                <Upload className="h-4 w-4" />
                              </Button>
                              <Button 
                                size="sm" 
                                className="gap-2"
                                onClick={() => setShowSpawnAgentDialog(true)}
                                disabled={!isInitialized}
                              >
                                <Plus className="h-4 w-4" />
                                <span className="hidden sm:inline">Spawn</span>
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            {(() => {
                              // Filter agents based on search results if searching
                              const displayAgents = isSearching && filters.entityType === 'agents' 
                                ? agents.filter(agent => 
                                    searchResults.some(result => result.id === agent.id && result.type === 'agent')
                                  )
                                : agents;

                              return displayAgents.length > 0 ? (
                                displayAgents.map((agent) => {
                                  const searchResult = searchResults.find(r => r.id === agent.id);
                                  return (
                                    <div key={agent.id} className="p-4 rounded-lg bg-card/50 border border-border/20">
                                      <div className="flex items-start justify-between">
                                        <div className="flex items-center gap-3">
                                          <Checkbox
                                            checked={bulkOperations.isSelected(agent.id)}
                                            onCheckedChange={() => bulkOperations.toggleSelection(agent.id)}
                                          />
                                        </div>
                                        <div className="flex-1">
                                          <div className="flex items-center gap-3 mb-2">
                                            <h3 className="font-medium">{agent.name}</h3>
                                            <Badge variant={getStatusBadgeVariant(agent.status)} className="gap-1">
                                              {getStatusIcon(agent.status)}
                                              {agent.status}
                                            </Badge>
                                            <Badge variant="outline">{agent.type}</Badge>
                                            {searchResult && searchResult.relevanceScore > 0 && (
                                              <Badge variant="secondary" className="text-xs">
                                                Score: {searchResult.relevanceScore}
                                              </Badge>
                                            )}
                                          </div>
                                          <p className="text-sm text-muted-foreground mb-2">
                                            {agent.systemPrompt || 'No system prompt configured'}
                                          </p>
                                          {searchResult?.highlights && searchResult.highlights.length > 0 && (
                                            <div className="mb-2 text-xs text-muted-foreground bg-accent/50 p-2 rounded">
                                              {searchResult.highlights[0]}
                                            </div>
                                          )}
                                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                            <span>Priority: {agent.priority}</span>
                                            <span>Max Tasks: {agent.maxConcurrentTasks}</span>
                                            <span>Capabilities: {agent.capabilities.join(', ')}</span>
                                          </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          <Button variant="outline" size="sm">
                                            <Settings className="h-3 w-3" />
                                          </Button>
                                          <Button 
                                            variant="outline" 
                                            size="sm" 
                                            className="text-destructive hover:text-destructive"
                                            onClick={() => handleTerminateAgent(agent.id, agent.name)}
                                            disabled={agent.status === 'terminated'}
                                          >
                                            <Trash2 className="h-3 w-3" />
                                          </Button>
                                        </div>
                                      </div>
                                    </div>
                                  );
                                })
                              ) : (
                                <div className="text-center py-12">
                                  {isSearching ? (
                                    <>
                                      <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                      <h3 className="text-lg font-medium mb-2">No Agents Found</h3>
                                      <p className="text-muted-foreground mb-4">
                                        No agents match your search criteria
                                      </p>
                                      <Button
                                        variant="outline"
                                        onClick={clearFilters}
                                        className="gap-2"
                                      >
                                        <X className="h-4 w-4" />
                                        Clear Filters
                                      </Button>
                                    </>
                                  ) : (
                                    <>
                                      <Bot className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                      <h3 className="text-lg font-medium mb-2">No Active Agents</h3>
                                      <p className="text-muted-foreground mb-4">Spawn your first agent to begin orchestration</p>
                                      <Button 
                                        className="gap-2"
                                        onClick={() => setShowSpawnAgentDialog(true)}
                                        disabled={!isInitialized}
                                      >
                                        <Plus className="h-4 w-4" />
                                        Spawn Agent
                                      </Button>
                                    </>
                                  )}
                                </div>
                              );
                            })()}
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Filter Panel */}
                    {showFilterPanel && (
                      <div className="lg:col-span-1">
                        <Card className="sticky top-0">
                          <CardContent className="p-4">
                            <FilterPanel
                              filters={filters}
                              onFiltersChange={updateFilters}
                              onClearFilters={clearFilters}
                              filterOptions={getFilterOptions()}
                              savedFilters={savedFilters}
                              onLoadSavedFilter={loadSavedFilter}
                              onDeleteSavedFilter={deleteSavedFilter}
                            />
                          </CardContent>
                        </Card>
                      </div>
                    )}
                  </div>
                </TabsContent>

                {/* Tasks Panel */}
                <TabsContent value="tasks" className="m-0">
                  <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    {/* Main Tasks Card */}
                    <div className="lg:col-span-3">
                      <Card className="border-green-500/20 bg-gradient-to-br from-green-500/5 to-blue-500/5">
                        <CardHeader>
                          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
                            <CardTitle className="flex items-center gap-2">
                              <Zap className="h-5 w-5 text-green-500" />
                              Task Queue ({tasks.length})
                            </CardTitle>
                            <div className="flex items-center gap-2 w-full sm:w-auto">
                              <SearchBar
                                value={filters.query}
                                onChange={(query) => updateFilters({ query })}
                                onSearch={() => search(filters)}
                                onClear={() => updateFilters({ query: '' })}
                                searchHistory={searchHistory}
                                isSearching={false}
                                onSaveFilter={() => {
                                  const name = prompt('Save filter as:');
                                  if (name) saveFilter(name);
                                }}
                                placeholder="Search tasks..."
                                className="flex-1 sm:w-64"
                              />
                              <Button
                                variant="outline"
                                size="icon"
                                onClick={() => setShowFilterPanel(!showFilterPanel)}
                                className={showFilterPanel ? 'bg-accent' : ''}
                              >
                                <Filter className="h-4 w-4" />
                              </Button>
                              <Button 
                                size="sm" 
                                className="gap-2"
                                onClick={() => setShowCreateTaskDialog(true)}
                                disabled={!isInitialized}
                              >
                                <Plus className="h-4 w-4" />
                                <span className="hidden sm:inline">Create</span>
                              </Button>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            {(() => {
                              // Filter tasks based on search results if searching
                              const displayTasks = isSearching && filters.entityType === 'tasks' 
                                ? tasks.filter(task => 
                                    searchResults.some(result => result.id === task.id && result.type === 'task')
                                  )
                                : tasks;

                              return displayTasks.length > 0 ? (
                                displayTasks.map((task) => {
                                  const searchResult = searchResults.find(r => r.id === task.id);
                                  return (
                                    <div key={task.id} className="p-4 rounded-lg bg-card/50 border border-border/20">
                                      <div className="flex items-start justify-between">
                                        <div className="flex items-center gap-3">
                                          <Checkbox
                                            checked={bulkOperations.isSelected(task.id)}
                                            onCheckedChange={() => bulkOperations.toggleSelection(task.id)}
                                          />
                                        </div>
                                        <div className="flex-1">
                                          <div className="flex items-center gap-3 mb-2">
                                            <h3 className="font-medium">{task.description}</h3>
                                            <Badge variant={getStatusBadgeVariant(task.status)} className="gap-1">
                                              {getStatusIcon(task.status)}
                                              {task.status}
                                            </Badge>
                                            <Badge variant="outline">{task.type}</Badge>
                                            {searchResult && searchResult.relevanceScore > 0 && (
                                              <Badge variant="secondary" className="text-xs">
                                                Score: {searchResult.relevanceScore}
                                              </Badge>
                                            )}
                                          </div>
                                          {searchResult?.highlights && searchResult.highlights.length > 0 && (
                                            <div className="mb-2 text-xs text-muted-foreground bg-accent/50 p-2 rounded">
                                              {searchResult.highlights[0]}
                                            </div>
                                          )}
                                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                            <span>Priority: {task.priority}</span>
                                            {task.agentId && <span>Agent: {task.agentId}</span>}
                                            <span>Created: {new Date(task.createdAt).toLocaleDateString()}</span>
                                          </div>
                                        </div>
                                        <div className="flex items-center gap-2">
                                          <Button variant="outline" size="sm">
                                            <Settings className="h-3 w-3" />
                                          </Button>
                                          <Button 
                                            variant="outline" 
                                            size="sm" 
                                            className="text-destructive hover:text-destructive"
                                            onClick={() => handleCancelTask(task.id)}
                                            disabled={task.status === 'completed' || task.status === 'cancelled'}
                                          >
                                            <XCircle className="h-3 w-3" />
                                          </Button>
                                        </div>
                                      </div>
                                    </div>
                                  );
                                })
                              ) : (
                                <div className="text-center py-12">
                                  {isSearching ? (
                                    <>
                                      <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                      <h3 className="text-lg font-medium mb-2">No Tasks Found</h3>
                                      <p className="text-muted-foreground mb-4">
                                        No tasks match your search criteria
                                      </p>
                                      <Button
                                        variant="outline"
                                        onClick={clearFilters}
                                        className="gap-2"
                                      >
                                        <X className="h-4 w-4" />
                                        Clear Filters
                                      </Button>
                                    </>
                                  ) : (
                                    <>
                                      <Zap className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                                      <h3 className="text-lg font-medium mb-2">No Active Tasks</h3>
                                      <p className="text-muted-foreground mb-4">Create tasks to distribute work among agents</p>
                                      <Button 
                                        className="gap-2"
                                        onClick={() => setShowCreateTaskDialog(true)}
                                        disabled={!isInitialized}
                                      >
                                        <Plus className="h-4 w-4" />
                                        Create Task
                                      </Button>
                                    </>
                                  )}
                                </div>
                              );
                            })()}
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Filter Panel */}
                    {showFilterPanel && (
                      <div className="lg:col-span-1">
                        <Card className="sticky top-0">
                          <CardContent className="p-4">
                            <FilterPanel
                              filters={filters}
                              onFiltersChange={updateFilters}
                              onClearFilters={clearFilters}
                              filterOptions={getFilterOptions()}
                              savedFilters={savedFilters}
                              onLoadSavedFilter={loadSavedFilter}
                              onDeleteSavedFilter={deleteSavedFilter}
                            />
                          </CardContent>
                        </Card>
                      </div>
                    )}
                  </div>
                </TabsContent>

                {/* Workflows Panel */}
                <TabsContent value="workflows" className="m-0">
                  <ClaudeFlowWorkflow integration={integration} />
                </TabsContent>

                {/* Memory Panel */}
                <TabsContent value="memory" className="m-0">
                  <ClaudeFlowMemory integration={integration} />
                </TabsContent>

                {/* Monitoring Panel */}
                <TabsContent value="monitoring" className="m-0">
                  <ClaudeFlowMonitoring integration={integration} />
                </TabsContent>

                {/* Terminal Panel */}
                <TabsContent value="terminal" className="m-0">
                  <ClaudeFlowTerminal integration={integration} />
                </TabsContent>
              </div>
            </Tabs>
          </nav>
        </main>
      </div>

      {/* Toast Notifications */}
      <ToastContainer>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onDismiss={() => {
              setToast(null);
              if (toastTimeoutRef.current) {
                clearTimeout(toastTimeoutRef.current);
                toastTimeoutRef.current = null;
              }
            }}
          />
        )}
      </ToastContainer>

      {/* Spawn Agent Dialog */}
      <SpawnAgentDialog
        open={showSpawnAgentDialog}
        onOpenChange={setShowSpawnAgentDialog}
        onSpawnAgent={handleSpawnAgent}
        loading={spawnAgentLoading}
      />

      {/* Create Task Dialog */}
      <CreateTaskDialog
        open={showCreateTaskDialog}
        onOpenChange={setShowCreateTaskDialog}
        onCreateTask={handleCreateTask}
        agents={agents}
        loading={createTaskLoading}
      />

      {/* Keyboard Shortcuts Help Dialog */}
      <ClaudeFlowKeyboardShortcutsHelp
        open={showKeyboardShortcutsHelp}
        onOpenChange={setShowKeyboardShortcutsHelp}
        shortcuts={shortcuts}
        formatShortcut={formatShortcut}
      />

      {/* Bulk Action Bar */}
      {(activeTab === 'agents' || activeTab === 'tasks') && bulkOperations.selectedCount > 0 && (
        <BulkActionBar
          type={activeTab === 'agents' ? 'agent' : 'task'}
          selectedCount={bulkOperations.selectedCount}
          totalCount={activeTab === 'agents' ? agents.length : tasks.length}
          onClearSelection={bulkOperations.clearSelection}
          onBulkAction={handleBulkAction}
          onSelectAll={() => {
            const allIds = activeTab === 'agents' 
              ? agents.map(a => a.id)
              : tasks.map(t => t.id);
            bulkOperations.selectAll(allIds);
          }}
          availableAgents={agents.filter(a => a.status !== 'terminated').map(a => ({ id: a.id, name: a.name }))}
        />
      )}

      {/* Bulk Operation Dialog */}
      <BulkOperationDialog
        open={showBulkOperationDialog}
        onOpenChange={(open) => {
          if (!open && !bulkOperations.isExecuting) {
            setShowBulkOperationDialog(false);
            bulkOperations.cancelOperation();
          }
        }}
        operation={bulkOperations.pendingOperation}
        onConfirm={confirmBulkOperation}
        onCancel={cancelBulkOperation}
        isExecuting={bulkOperations.isExecuting}
        progress={bulkOperations.progress}
        result={bulkOperations.result}
      />
    </div>
  );
};

/**
 * Dialog for spawning a new agent
 */
const SpawnAgentDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSpawnAgent: (config: any) => void;
  loading: boolean;
}> = ({ open, onOpenChange, onSpawnAgent, loading }) => {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [useTemplate, setUseTemplate] = useState(true);
  const [agentType, setAgentType] = useState('researcher');
  const [agentName, setAgentName] = useState('');
  const [systemPrompt, setSystemPrompt] = useState('');
  const [priority, setPriority] = useState(5);
  const [maxConcurrentTasks, setMaxConcurrentTasks] = useState(3);
  const [capabilities, setCapabilities] = useState<string[]>([]);

  // Apply template when selected
  const applyTemplate = (templateId: string) => {
    const template = AGENT_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      setAgentType(template.type);
      setAgentName(template.name);
      setSystemPrompt(template.systemPrompt);
      setPriority(template.priority);
      setMaxConcurrentTasks(template.maxConcurrentTasks);
      setCapabilities(template.capabilities);
    }
  };

  // Handle template selection
  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    applyTemplate(templateId);
  };

  const handleSubmit = () => {
    if (!agentName.trim()) {
      return;
    }

    onSpawnAgent({
      type: agentType,
      name: agentName,
      systemPrompt: systemPrompt || undefined,
      priority,
      maxConcurrentTasks,
      capabilities: capabilities.length > 0 ? capabilities : undefined
    });
  };

  const handleCapabilitiesChange = (value: string) => {
    const caps = value.split(',').map(c => c.trim()).filter(c => c);
    setCapabilities(caps);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Bot className="h-5 w-5 text-blue-500" />
            Spawn New Agent
          </DialogTitle>
          <DialogDescription>
            Choose from predefined templates or create a custom agent configuration
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 py-4 overflow-y-auto max-h-[60vh]">
          {/* Template Selection */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">Agent Templates</Label>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setUseTemplate(!useTemplate)}
                className="gap-2"
              >
                <LayoutTemplate className="h-4 w-4" />
                {useTemplate ? 'Custom' : 'Templates'}
              </Button>
            </div>

            {useTemplate ? (
              <div className="grid gap-3 max-h-[400px] overflow-y-auto pr-2">
                {AGENT_TEMPLATES.map((template) => (
                  <motion.div
                    key={template.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`p-3 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                      selectedTemplate === template.id
                        ? 'border-blue-500 bg-blue-500/10'
                        : 'border-border hover:border-blue-300'
                    }`}
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    <div className="flex items-start gap-3">
                      <div className={`p-2 rounded-md bg-background ${template.color}`}>
                        {template.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm">{template.name}</h4>
                        <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {template.description}
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="secondary" className="text-xs">
                            Priority: {template.priority}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {template.maxConcurrentTasks} tasks
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Sparkles className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>Switch to custom mode to configure agent manually</p>
              </div>
            )}
          </div>

          {/* Configuration Panel */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Agent Configuration</Label>
            
            {/* Agent Type */}
            <div className="space-y-2">
              <Label htmlFor="agent-type">Agent Type</Label>
              <Select value={agentType} onValueChange={setAgentType} disabled={useTemplate && !!selectedTemplate}>
                <SelectTrigger id="agent-type">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="researcher">
                    <div className="flex items-center gap-2">
                      <Search className="h-4 w-4" />
                      Researcher
                    </div>
                  </SelectItem>
                  <SelectItem value="implementer">
                    <div className="flex items-center gap-2">
                      <Code className="h-4 w-4" />
                      Implementer
                    </div>
                  </SelectItem>
                  <SelectItem value="reviewer">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Reviewer
                    </div>
                  </SelectItem>
                  <SelectItem value="security">
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      Security Analyst
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Agent Name */}
            <div className="space-y-2">
              <Label htmlFor="agent-name">Agent Name</Label>
              <Input
                id="agent-name"
                placeholder="e.g., Research Assistant"
                value={agentName}
                onChange={(e) => setAgentName(e.target.value)}
              />
            </div>

            {/* System Prompt */}
            <div className="space-y-2">
              <Label htmlFor="system-prompt">System Prompt</Label>
              <Textarea
                id="system-prompt"
                placeholder="Define the agent's behavior and specialization..."
                value={systemPrompt}
                onChange={(e) => setSystemPrompt(e.target.value)}
                rows={3}
                className="resize-none"
              />
            </div>

            {/* Capabilities */}
            <div className="space-y-2">
              <Label htmlFor="capabilities">Capabilities</Label>
              <Input
                id="capabilities"
                placeholder="e.g., web-search, analysis, reporting"
                value={capabilities.join(', ')}
                onChange={(e) => handleCapabilitiesChange(e.target.value)}
              />
            </div>

            {/* Priority & Tasks */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="priority">Priority: {priority}</Label>
                <Slider
                  id="priority"
                  min={1}
                  max={10}
                  step={1}
                  value={[priority]}
                  onValueChange={(value) => setPriority(value[0])}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="max-tasks">Max Tasks: {maxConcurrentTasks}</Label>
                <Slider
                  id="max-tasks"
                  min={1}
                  max={10}
                  step={1}
                  value={[maxConcurrentTasks]}
                  onValueChange={(value) => setMaxConcurrentTasks(value[0])}
                />
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading || !agentName.trim()}>
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Spawning...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Spawn Agent
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

/**
 * Dialog for creating a new task
 */
const CreateTaskDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateTask: (config: any) => void;
  agents: ClaudeFlowAgent[];
  loading: boolean;
}> = ({ open, onOpenChange, onCreateTask, agents, loading }) => {
  const [useTemplate, setUseTemplate] = useState(true);
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [taskType, setTaskType] = useState('research');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState(5);
  const [assignToAgent, setAssignToAgent] = useState('');
  const [assignToAgentType, setAssignToAgentType] = useState('');
  const [timeout, setTimeout] = useState(3600); // 1 hour default

  // Apply template when selected
  const applyTemplate = (templateId: string) => {
    const template = TASK_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      setTaskType(template.type);
      setDescription(template.description);
      setPriority(template.priority);
      setAssignToAgentType(template.preferredAgentType || '');
      setTimeout(template.estimatedDuration || 3600);
    }
  };

  // Handle template selection
  const handleTemplateSelect = (templateId: string) => {
    setSelectedTemplate(templateId);
    applyTemplate(templateId);
  };

  const handleSubmit = () => {
    if (!description.trim()) {
      return;
    }

    onCreateTask({
      type: taskType,
      description,
      priority,
      assignToAgent: assignToAgent || undefined,
      assignToAgentType: assignToAgentType || undefined,
      timeout: timeout > 0 ? timeout : undefined
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-green-500" />
            Create New Task
          </DialogTitle>
          <DialogDescription>
            Choose from predefined templates or create a custom task configuration
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 py-4 overflow-y-auto max-h-[60vh]">
          {/* Template Selection */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">Task Templates</Label>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setUseTemplate(!useTemplate)}
                className="gap-2"
              >
                <LayoutTemplate className="h-4 w-4" />
                {useTemplate ? 'Custom' : 'Templates'}
              </Button>
            </div>

            {useTemplate ? (
              <div className="grid gap-3 max-h-[400px] overflow-y-auto pr-2">
                {TASK_TEMPLATES.map((template) => (
                  <motion.div
                    key={template.id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className={`p-3 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                      selectedTemplate === template.id
                        ? 'border-green-500 bg-green-500/10'
                        : 'border-border hover:border-green-300'
                    }`}
                    onClick={() => handleTemplateSelect(template.id)}
                  >
                    <div className="flex items-start gap-3">
                      <div className={`p-2 rounded-md bg-background ${template.color}`}>
                        {template.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm">{template.name}</h4>
                        <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {template.description}
                        </p>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge variant="secondary" className="text-xs">
                            {template.type}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            Priority: {template.priority}
                          </Badge>
                          {template.estimatedDuration && (
                            <Badge variant="outline" className="text-xs">
                              ~{Math.round(template.estimatedDuration/60)}min
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Sparkles className="h-12 w-12 mx-auto mb-3 opacity-50" />
                <p>Switch to custom mode to configure task manually</p>
              </div>
            )}
          </div>

          {/* Configuration Panel */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Task Configuration</Label>
            
            {/* Task Type */}
            <div className="space-y-2">
              <Label htmlFor="task-type">Task Type</Label>
              <Select value={taskType} onValueChange={setTaskType} disabled={useTemplate && !!selectedTemplate}>
                <SelectTrigger id="task-type">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="research">
                    <div className="flex items-center gap-2">
                      <Search className="h-4 w-4" />
                      Research
                    </div>
                  </SelectItem>
                  <SelectItem value="implementation">
                    <div className="flex items-center gap-2">
                      <Code className="h-4 w-4" />
                      Implementation
                    </div>
                  </SelectItem>
                  <SelectItem value="review">
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      Review
                    </div>
                  </SelectItem>
                  <SelectItem value="analysis">
                    <div className="flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      Analysis
                    </div>
                  </SelectItem>
                  <SelectItem value="testing">
                    <div className="flex items-center gap-2">
                      <TestTube className="h-4 w-4" />
                      Testing
                    </div>
                  </SelectItem>
                  <SelectItem value="documentation">
                    <div className="flex items-center gap-2">
                      <BookOpen className="h-4 w-4" />
                      Documentation
                    </div>
                  </SelectItem>
                  <SelectItem value="bug-fix">
                    <div className="flex items-center gap-2">
                      <Bug className="h-4 w-4" />
                      Bug Fix
                    </div>
                  </SelectItem>
                  <SelectItem value="performance">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4" />
                      Performance
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Task Description</Label>
              <Textarea
                id="description"
                placeholder={useTemplate && selectedTemplate ? "Description from template..." : "Describe what needs to be done..."}
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={4}
                className="resize-none"
              />
            </div>

            {/* Assignment */}
            <div className="space-y-2">
              <Label>Assignment (Optional)</Label>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="assign-agent" className="text-sm text-muted-foreground">Specific Agent</Label>
                  <Select value={assignToAgent} onValueChange={setAssignToAgent}>
                    <SelectTrigger id="assign-agent">
                      <SelectValue placeholder="Auto-assign" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Auto-assign</SelectItem>
                      {agents.filter(a => a.status !== 'terminated').map(agent => (
                        <SelectItem key={agent.id} value={agent.id}>
                          {agent.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="assign-type" className="text-sm text-muted-foreground">Agent Type</Label>
                  <Select value={assignToAgentType} onValueChange={setAssignToAgentType}>
                    <SelectTrigger id="assign-type">
                      <SelectValue placeholder="Any type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Any type</SelectItem>
                      <SelectItem value="researcher">Researcher</SelectItem>
                      <SelectItem value="implementer">Implementer</SelectItem>
                      <SelectItem value="reviewer">Reviewer</SelectItem>
                      <SelectItem value="security">Security Analyst</SelectItem>
                      <SelectItem value="analyst">Data Analyst</SelectItem>
                      <SelectItem value="tester">Test Engineer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Priority & Timeout */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="task-priority">Priority: {priority}</Label>
                <Slider
                  id="task-priority"
                  min={1}
                  max={10}
                  step={1}
                  value={[priority]}
                  onValueChange={(value) => setPriority(value[0])}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="timeout">Timeout: {timeout > 0 ? `${Math.round(timeout/60)}min` : 'None'}</Label>
                <Slider
                  id="timeout"
                  min={0}
                  max={7200} // 2 hours max
                  step={300} // 5 minute increments
                  value={[timeout]}
                  onValueChange={(value) => setTimeout(value[0])}
                />
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading || !description.trim()}>
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4 mr-2" />
                Create Task
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

// Wrap the component with error boundary
const ClaudeFlowManager: React.FC<ClaudeFlowManagerProps> = (props) => {
  return (
    <ClaudeFlowErrorBoundary>
      <ClaudeFlowManagerInner {...props} />
    </ClaudeFlowErrorBoundary>
  );
};

export default ClaudeFlowManager;