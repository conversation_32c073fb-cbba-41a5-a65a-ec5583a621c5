import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { AlertCircle, XCircle, Loader2 } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { BulkOperation, BulkOperationResult, BulkOperationProgress } from '@/lib/bulkOperationEngine';

interface BulkOperationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  operation: BulkOperation | null;
  onConfirm: () => void;
  onCancel: () => void;
  isExecuting: boolean;
  progress: BulkOperationProgress | null;
  result: BulkOperationResult | null;
}

export function BulkOperationDialog({
  open,
  onOpenChange,
  operation,
  onConfirm,
  onCancel,
  isExecuting,
  progress,
  result,
}: BulkOperationDialogProps) {
  if (!operation) return null;

  const getActionDescription = () => {
    const count = operation.targetIds.length;
    const entityType = operation.type === 'agent' ? 'agent' : 'task';
    const plural = count !== 1 ? 's' : '';

    switch (operation.action) {
      case 'terminate':
        return `terminate ${count} ${entityType}${plural}`;
      case 'cancel':
        return `cancel ${count} ${entityType}${plural}`;
      case 'changePriority':
        return `change priority of ${count} ${entityType}${plural} to ${operation.parameters?.priority}`;
      case 'reassign':
        return `reassign ${count} ${entityType}${plural} to another agent`;
      case 'delete':
        return `delete ${count} ${entityType}${plural}`;
      case 'export':
        return `export ${count} ${entityType}${plural}`;
      case 'duplicate':
      case 'clone':
        return `${operation.action} ${count} ${entityType}${plural}`;
      default:
        return `perform ${operation.action} on ${count} ${entityType}${plural}`;
    }
  };

  const isDestructive = ['terminate', 'cancel', 'delete'].includes(operation.action);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        {!isExecuting && !result && (
          <>
            <DialogHeader>
              <DialogTitle>
                {isDestructive ? 'Confirm Destructive Action' : 'Confirm Bulk Operation'}
              </DialogTitle>
              <DialogDescription>
                You are about to {getActionDescription()}. This action{' '}
                {isDestructive ? 'cannot be undone' : 'will affect multiple items'}.
              </DialogDescription>
            </DialogHeader>

            <div className="my-4 p-4 bg-muted rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Operation Details</span>
              </div>
              <div className="space-y-1 text-sm text-muted-foreground">
                <div>Type: {operation.type}</div>
                <div>Action: {operation.action}</div>
                <div>Affected items: {operation.targetIds.length}</div>
                {operation.parameters && (
                  <div>
                    Parameters: {JSON.stringify(operation.parameters, null, 2)}
                  </div>
                )}
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button
                variant={isDestructive ? 'destructive' : 'default'}
                onClick={onConfirm}
              >
                Confirm
              </Button>
            </DialogFooter>
          </>
        )}

        {isExecuting && progress && (
          <>
            <DialogHeader>
              <DialogTitle>Executing Bulk Operation</DialogTitle>
              <DialogDescription>
                Processing {progress.current} of {progress.total} items...
              </DialogDescription>
            </DialogHeader>

            <div className="my-4 space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-muted-foreground">Progress</span>
                  <span className="font-medium">{progress.percentage}%</span>
                </div>
                <Progress value={progress.percentage} className="h-2" />
              </div>

              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>{progress.currentOperation}</span>
              </div>
            </div>
          </>
        )}

        {result && (
          <>
            <DialogHeader>
              <DialogTitle>Operation Complete</DialogTitle>
              <DialogDescription>
                The bulk operation has finished with the following results:
              </DialogDescription>
            </DialogHeader>

            <div className="my-4 space-y-4">
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {result.successful.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Successful</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {result.failed.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">{result.total}</div>
                  <div className="text-sm text-muted-foreground">Total</div>
                </div>
              </div>

              {result.failed.length > 0 && (
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <XCircle className="h-4 w-4 text-red-600" />
                    <span className="text-sm font-medium">Failed Operations</span>
                  </div>
                  <ScrollArea className="h-[200px] w-full rounded-md border p-4">
                    <div className="space-y-2">
                      {result.failed.map((failure, index) => (
                        <div
                          key={`${failure.id}-${index}`}
                          className="text-sm space-y-1"
                        >
                          <div className="font-medium">ID: {failure.id}</div>
                          <div className="text-muted-foreground">
                            Error: {failure.error}
                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button onClick={() => onOpenChange(false)}>Close</Button>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}