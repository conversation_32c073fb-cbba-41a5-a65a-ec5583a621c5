import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  ArrowLeft,
  Terminal,
  FolderOpen,
  ChevronDown,
  ChevronUp,
  X,
  Hash,
  Sidebar,
  Clock,
  Zap,
  Navigation,
  TrendingUp,
  Archive,
  Search,
  BarChart3,
  Activity,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { api, SessionMode, ModeSuggestion } from "@/lib/api";
import { cn } from "@/lib/utils";
import { open } from "@tauri-apps/plugin-dialog";
import { FloatingPromptInput, type FloatingPromptInputRef } from "./FloatingPromptInput";
import { ErrorBoundary } from "./ErrorBoundary";
import { SplitPane } from "@/components/ui/split-pane";
import { WebviewPreview } from "./WebviewPreview";
import { ModeTransitionDialog } from "./ModeTransitionDialog";
import { useSessionStore } from "@/stores/sessionStore";
import { ModeSuggestionBanner } from "./ModeSuggestionBanner";
import { recordModeTransition } from "@/lib/modeHistory";
import { useTodoSync } from "@/hooks/useTodoSync";
import { getModePreferences } from "@/lib/modePreferences";
import { FollowUpQuestion } from "@/lib/followUpQuestions";
import { MessageVirtualizer } from "./MessageVirtualizer";
import { useSessionState } from "@/hooks/useSessionState";
import { useSessionEventHandler } from "@/hooks/useSessionEventHandler";
import { useSessionHistory } from "@/hooks/useSessionHistory";
import { Settings, Wrench, FileText } from "lucide-react";

// New panel system imports
// import { usePanelKeyboardShortcuts } from "@/hooks/usePanelKeyboardShortcuts";

import type { Session } from "@/lib/api";

interface ClaudeCodeSessionProps {
  /**
   * Optional session to resume (when clicking from SessionList)
   */
  session?: Session;
  /**
   * Initial project path (for new sessions)
   */
  initialProjectPath?: string;
  /**
   * Callback to go back
   */
  onBack: () => void;
  /**
   * Callback to open hooks configuration
   */
  onProjectSettings?: (projectPath: string) => void;
  /**
   * Optional className for styling
   */
  className?: string;
  /**
   * Callback when streaming state changes
   */
  onStreamingChange?: (isStreaming: boolean, sessionId: string | null) => void;
}

/**
 * ClaudeCodeSession component for interactive Claude Code sessions
 * 
 * @example
 * <ClaudeCodeSession onBack={() => setView('projects')} />
 */
export const ClaudeCodeSession: React.FC<ClaudeCodeSessionProps> = ({
  session,
  initialProjectPath = "",
  onBack,
  className,
  onStreamingChange,
}) => {
  // Extract state management to custom hook
  const sessionState = useSessionState({
    session,
    initialProjectPath,
    onStreamingChange,
  });
  
  const {
    projectPath,
    setProjectPath,
    messages,
    setMessages,
    isLoading,
    setIsLoading,
    error,
    setError,
    claudeSessionId,
    setClaudeSessionId,
    setExtractedSessionInfo,
    effectiveSession,
    isFirstPrompt,
    setIsFirstPrompt,
    currentMode,
    setCurrentMode,
    rawJsonlOutput,
    setRawJsonlOutput,
    totalTokens,
    showFollowUpQuestions,
    setShowFollowUpQuestions,
    recentTools,
    setRecentTools,
    currentFiles,
    setCurrentFiles,
    showPreview,
    setShowPreview,
    previewUrl,
    setPreviewUrl,
    isPreviewMaximized,
    setIsPreviewMaximized,
    splitPosition,
    setSplitPosition,
    showPreviewPrompt,
    setShowPreviewPrompt,
    queuedPrompts,
    setQueuedPrompts,
    queuedPromptsCollapsed,
    setQueuedPromptsCollapsed,
  } = sessionState;
  
  // Additional local state
  const [modeSuggestion, setModeSuggestion] = useState<ModeSuggestion | null>(null);
  const [autoModeTriggersEnabled, setAutoModeTriggersEnabled] = useState(true);
  const [showModeTransitionDialog, setShowModeTransitionDialog] = useState(false);
  const [pendingModeTransition, setPendingModeTransition] = useState<{
    from: SessionMode;
    to: SessionMode;
  } | null>(null);
  
  // Session Context Sidebar state
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarActiveTab, setSidebarActiveTab] = useState<'navigation' | 'context' | 'timeline' | 'stats'>('navigation');
  
  // Session store hooks
  const { switchSessionMode, handleModeChange } = useSessionStore();
  
  // Refs
  const floatingPromptRef = useRef<FloatingPromptInputRef>(null);
  const queuedPromptsRef = useRef<Array<{ id: string; prompt: string; model: "sonnet" | "opus" }>>([]);
  const hasActiveSessionRef = useRef(false);
  const isMountedRef = useRef(true);

  // Keep ref in sync with state
  useEffect(() => {
    queuedPromptsRef.current = queuedPrompts;
  }, [queuedPrompts]);

  // Sync todos with session store
  useTodoSync(messages, effectiveSession?.id);

  // Extract session history management
  const sessionHistory = useSessionHistory();
  
  // Forward declaration for handleSendPrompt
  const handleSendPromptRef = useRef<(prompt: string, model: "sonnet" | "opus") => Promise<void>>();
  
  // Enhanced navigation state and refs
  const messageVirtualizerRef = useRef<HTMLDivElement>(null);
  const [scrollPosition, setScrollPosition] = useState<'top' | 'middle' | 'bottom'>('bottom');
  const [canScrollUp, setCanScrollUp] = useState(false);
  const [canScrollDown, setCanScrollDown] = useState(false);
  
  // Extract event handling to custom hook
  const eventHandler = useSessionEventHandler({
    effectiveSession,
    claudeSessionId,
    projectPath,
    isLoading,
    isMountedRef,
    setMessages,
    setRawJsonlOutput,
    setError,
    setIsLoading,
    setClaudeSessionId,
    setExtractedSessionInfo,
    setCurrentMode,
    setRecentTools,
    setCurrentFiles,
    setShowFollowUpQuestions,
    handleModeChange,
    queuedPromptsRef,
    setQueuedPrompts,
    handleSendPrompt: async (prompt, model) => {
      if (handleSendPromptRef.current) {
        await handleSendPromptRef.current(prompt, model);
      }
    },
    hasActiveSessionRef,
  });

  // Check auto mode triggers setting when project path changes
  useEffect(() => {
    if (projectPath) {
      api.getAutoModeTriggersEnabled(projectPath)
        .then(setAutoModeTriggersEnabled)
        .catch(err => {
          console.error('Failed to get auto mode triggers setting:', err);
          setAutoModeTriggersEnabled(true);
        });
    }
  }, [projectPath]);

  const handleSelectPath = async () => {
    try {
      const selected = await open({
        directory: true,
        multiple: false,
        title: "Select Project Directory"
      });
      
      if (selected) {
        setProjectPath(selected as string);
        setError(null);
      }
    } catch (err) {
      console.error("Failed to select directory:", err);
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(`Failed to select directory: ${errorMessage}`);
    }
  };

  const handleSendPrompt = async (prompt: string, model: "sonnet" | "opus") => {
    setShowFollowUpQuestions(false);
    console.log('[ClaudeCodeSession] handleSendPrompt called with:', { prompt, model, projectPath, claudeSessionId, effectiveSession });
    
    if (!projectPath) {
      setError("Please select a project directory first");
      return;
    }

    // If already loading, queue the prompt
    if (isLoading) {
      const newPrompt = {
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        prompt,
        model
      };
      setQueuedPrompts(prev => [...prev, newPrompt]);
      return;
    }

    // Check for mode suggestions if auto triggers are enabled
    if (autoModeTriggersEnabled && effectiveSession) {
      try {
        const suggestion = await api.suggestModeSwitch(prompt, currentMode);
        if (suggestion) {
          const prefs = await getModePreferences();
          if (prefs.auto_suggestions_enabled && suggestion.confidence >= prefs.auto_suggestion_threshold) {
            setModeSuggestion(suggestion);
          }
        }
      } catch (err) {
        console.error('Failed to get mode suggestion:', err);
      }
    }

    try {
      setIsLoading(true);
      setError(null);
      hasActiveSessionRef.current = true;
      
      if (effectiveSession && !claudeSessionId) {
        setClaudeSessionId(effectiveSession.id);
      }
      
      // Setup event listeners using the extracted handler
      await eventHandler.setupEventListeners(
        claudeSessionId || effectiveSession?.id || null,
        prompt
      );
      
      // Add the user message immediately to the UI
      const userMessage = sessionHistory.addUserMessage(prompt);
      setMessages(prev => [...prev, userMessage]);

      // Execute the appropriate command with mode
      if (effectiveSession && !isFirstPrompt) {
        console.log('[ClaudeCodeSession] Resuming session:', effectiveSession.id, 'in mode:', currentMode);
        try {
          await api.resumeClaudeCode(projectPath, effectiveSession.id, prompt, model, currentMode);
        } catch (err) {
          console.warn('[ClaudeCodeSession] Resume failed, starting new session:', err);
          setIsFirstPrompt(false);
          await api.executeClaudeCode(projectPath, prompt, model, currentMode);
        }
      } else {
        console.log('[ClaudeCodeSession] Starting new session in mode:', currentMode);
        setIsFirstPrompt(false);
        await api.executeClaudeCode(projectPath, prompt, model, currentMode);
      }
    } catch (err) {
      console.error("Failed to send prompt:", err);
      setError("Failed to send prompt");
      setIsLoading(false);
      hasActiveSessionRef.current = false;
    }
  };
  
  // Set the ref so the event handler can call it
  handleSendPromptRef.current = handleSendPrompt;

  // Enhanced navigation functions
  const handleScrollToTop = () => {
    // Access the scroll container directly
    const scrollContainer = messageVirtualizerRef.current?.querySelector('.h-full.overflow-y-auto');
    if (scrollContainer) {
      scrollContainer.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };

  const handleScrollToBottom = () => {
    // Access the scroll container directly
    const scrollContainer = messageVirtualizerRef.current?.querySelector('.h-full.overflow-y-auto') as HTMLElement;
    if (scrollContainer) {
      scrollContainer.scrollTo({
        top: scrollContainer.scrollHeight,
        behavior: 'smooth'
      });
    }
  };

  // Function to jump to a specific message
  const handleJumpToMessage = (messageIndex: number) => {
    // This is a simplified implementation - for full functionality,
    // we'd need to integrate with the MessageVirtualizer's scroll methods
    const scrollContainer = messageVirtualizerRef.current?.querySelector('.h-full.overflow-y-auto') as HTMLElement;
    if (scrollContainer) {
      // Estimate scroll position based on message index
      // In a real implementation, we'd get the actual message position from the virtualizer
      const estimatedPosition = (messageIndex / messages.length) * scrollContainer.scrollHeight;
      scrollContainer.scrollTo({
        top: estimatedPosition,
        behavior: 'smooth'
      });
    }
  };

  // Monitor scroll position for navigation state
  useEffect(() => {
    const updateScrollState = () => {
      if (messages.length <= 5) {
        setCanScrollUp(false);
        setCanScrollDown(false);
        return;
      }

      const scrollContainer = messageVirtualizerRef.current?.querySelector('.h-full.overflow-y-auto');
      if (!scrollContainer) return;

      const { scrollTop, scrollHeight, clientHeight } = scrollContainer;
      const scrollPercentage = scrollTop / (scrollHeight - clientHeight);
      
      setCanScrollUp(scrollTop > 100);
      setCanScrollDown(scrollTop < scrollHeight - clientHeight - 100);
      
      if (scrollPercentage < 0.1) {
        setScrollPosition('top');
      } else if (scrollPercentage > 0.9) {
        setScrollPosition('bottom');
      } else {
        setScrollPosition('middle');
      }
    };

    updateScrollState();
    
    const scrollContainer = messageVirtualizerRef.current?.querySelector('.h-full.overflow-y-auto');
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', updateScrollState);
      return () => scrollContainer.removeEventListener('scroll', updateScrollState);
    }
  }, [messages.length]);

  const handleAcceptModeSuggestion = async () => {
    if (!modeSuggestion || !effectiveSession) return;
    
    try {
      // Record the transition with auto-suggestion reason
      await recordModeTransition(
        effectiveSession.id,
        effectiveSession.project_id,
        currentMode,
        modeSuggestion.suggested_mode,
        { 
          type: 'AutoSuggestion', 
          confidence: modeSuggestion.confidence,
          keywords: []
        },
        modeSuggestion.reason
      );
      
      await switchSessionMode(
        effectiveSession.id,
        effectiveSession.project_id,
        modeSuggestion.suggested_mode,
        modeSuggestion.reason
      );
      setModeSuggestion(null);
    } catch (err) {
      console.error('Failed to switch mode:', err);
      setError('Failed to switch mode');
    }
  };

  const handleDismissModeSuggestion = () => {
    setModeSuggestion(null);
  };



  const handleCancelExecution = async () => {
    if (!claudeSessionId || !isLoading) return;
    
    try {
      await api.cancelClaudeExecution(claudeSessionId);
      
      // Reset states
      setIsLoading(false);
      hasActiveSessionRef.current = false;
      setError(null);
      setQueuedPrompts([]);
      
      // Add a message indicating the session was cancelled
      const cancelMessage = sessionHistory.addSystemMessage("Session cancelled by user", "info");
      setMessages(prev => [...prev, cancelMessage]);
    } catch (err) {
      console.error("Failed to cancel execution:", err);
      
      const errorMessage = sessionHistory.addSystemMessage(
        `Failed to cancel execution: ${err instanceof Error ? err.message : 'Unknown error'}. The process may still be running in the background.`,
        "error"
      );
      setMessages(prev => [...prev, errorMessage]);
      
      // Reset states to allow user to continue
      setIsLoading(false);
      hasActiveSessionRef.current = false;
      setError(null);
    }
  };

  // Handle URL detection from terminal output
  const handleLinkDetected = (url: string) => {
    if (!showPreview && !showPreviewPrompt) {
      setPreviewUrl(url);
      setShowPreviewPrompt(true);
    }
  };

  const handleClosePreview = () => {
    setShowPreview(false);
    setIsPreviewMaximized(false);
    // Keep the previewUrl so it can be restored when reopening
  };

  const handlePreviewUrlChange = (url: string) => {
    console.log('[ClaudeCodeSession] Preview URL changed to:', url);
    setPreviewUrl(url);
  };

  const handleTogglePreviewMaximize = () => {
    setIsPreviewMaximized(!isPreviewMaximized);
    // Reset split position when toggling maximize
    if (isPreviewMaximized) {
      setSplitPosition(50);
    }
  };

  // Session Context Sidebar handlers
  const handleToggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSidebarTabChange = (tab: 'navigation' | 'context' | 'timeline' | 'stats') => {
    setSidebarActiveTab(tab);
    if (!sidebarOpen) {
      setSidebarOpen(true);
    }
  };

  // Track mount state
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Keyboard shortcuts for sidebar
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + Shift + S to toggle sidebar
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'S') {
        e.preventDefault();
        handleToggleSidebar();
      }
      // Cmd/Ctrl + Shift + N for navigation tab
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'N') {
        e.preventDefault();
        handleSidebarTabChange('navigation');
      }
      // Cmd/Ctrl + Shift + C for context tab
      if ((e.metaKey || e.ctrlKey) && e.shiftKey && e.key === 'C') {
        e.preventDefault();
        handleSidebarTabChange('context');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [sidebarOpen]);

  // Session Context Sidebar Component
  const SessionContextSidebar = () => (
    <AnimatePresence>
      {sidebarOpen && (
        <motion.div
          initial={{ x: '100%', opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: '100%', opacity: 0 }}
          transition={{ type: 'spring', damping: 20, stiffness: 100 }}
          className="fixed top-0 right-0 h-full w-80 lg:w-96 bg-background/95 backdrop-blur-md border-l border-border/50 shadow-2xl z-40 flex flex-col"
        >
          {/* Sidebar Header */}
          <div className="flex items-center justify-between p-4 border-b border-border/50">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
                <Sidebar className="h-4 w-4 text-primary" />
              </div>
              <div>
                <h2 className="font-semibold text-foreground">Session Context</h2>
                <p className="text-xs text-muted-foreground">Navigate and manage your session</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              onClick={handleToggleSidebar}
              className="h-8 w-8"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Tab Navigation */}
          <div className="flex items-center border-b border-border/30">
            {[
              { id: 'navigation', label: 'Navigate', icon: Navigation },
              { id: 'context', label: 'Context', icon: Archive },
              { id: 'timeline', label: 'Timeline', icon: Clock },
              { id: 'stats', label: 'Stats', icon: BarChart3 },
            ].map((tab) => (
              <Button
                key={tab.id}
                variant={sidebarActiveTab === tab.id ? "default" : "ghost"}
                size="sm"
                onClick={() => setSidebarActiveTab(tab.id as any)}
                className={cn(
                  "flex-1 rounded-none border-r border-border/30 last:border-r-0 transition-all duration-200",
                  sidebarActiveTab === tab.id 
                    ? "bg-primary/10 text-primary border-b-2 border-b-primary" 
                    : "hover:bg-accent/50"
                )}
              >
                <tab.icon className="h-3.5 w-3.5 mr-1.5" />
                <span className="text-xs font-medium">{tab.label}</span>
              </Button>
            ))}
          </div>

          {/* Tab Content */}
          <div className="flex-1 overflow-auto p-4 space-y-4">
            {/* Navigation Tab */}
            {sidebarActiveTab === 'navigation' && (
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-semibold mb-3 flex items-center gap-2">
                    <Navigation className="h-4 w-4 text-primary" />
                    Quick Navigation
                  </h3>
                  <div className="space-y-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => {
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                      }}
                    >
                      <ChevronUp className="h-4 w-4 mr-2" />
                      Scroll to Top
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start"
                      onClick={() => {
                        window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
                      }}
                    >
                      <ChevronDown className="h-4 w-4 mr-2" />
                      Scroll to Bottom
                    </Button>
                  </div>
                </div>

                {messages.length > 0 && (
                  <div>
                    <h3 className="text-sm font-semibold mb-3 flex items-center gap-2">
                      <Search className="h-4 w-4 text-primary" />
                      Quick Jump
                    </h3>
                    <div className="space-y-1 max-h-48 overflow-y-auto">
                      {messages.slice(-10).reverse().map((message, index) => (
                        <Button
                          key={`${message.id}-${index}`}
                          variant="ghost"
                          size="sm"
                          className="w-full justify-start text-left p-2 h-auto"
                        >
                          <div className="flex items-start gap-2 w-full">
                            <div className={cn(
                              "h-2 w-2 rounded-full mt-1.5 flex-shrink-0",
                              message.role === 'user' ? "bg-blue-500" : "bg-green-500"
                            )} />
                            <div className="flex-1 min-w-0">
                              <p className="text-xs text-muted-foreground">
                                {message.role === 'user' ? 'You' : 'Claude'} • Just now
                              </p>
                              <p className="text-sm line-clamp-2 break-words">
                                {message.content?.slice(0, 60) || 'No content'}...
                              </p>
                            </div>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Context Tab */}
            {sidebarActiveTab === 'context' && (
              <div className="space-y-4">
                {recentTools.length > 0 && (
                  <div>
                    <h3 className="text-sm font-semibold mb-3 flex items-center gap-2">
                      <Zap className="h-4 w-4 text-primary" />
                      Recent Tools
                    </h3>
                    <div className="space-y-1">
                      {recentTools.slice(0, 5).map((tool, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-3 p-2 rounded-lg bg-muted/30 border border-border/30"
                        >
                          <div className="p-1.5 rounded-md bg-accent/10 border border-accent/20">
                            <Wrench className="h-3 w-3 text-accent" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium">{tool}</p>
                            <p className="text-xs text-muted-foreground">Recently used</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {currentFiles.length > 0 && (
                  <div>
                    <h3 className="text-sm font-semibold mb-3 flex items-center gap-2">
                      <FileText className="h-4 w-4 text-primary" />
                      Current Files
                    </h3>
                    <div className="space-y-1">
                      {currentFiles.slice(0, 5).map((file, index) => (
                        <div
                          key={index}
                          className="flex items-center gap-3 p-2 rounded-lg bg-muted/30 border border-border/30"
                        >
                          <div className="p-1.5 rounded-md bg-blue-100/10 border border-blue-200/20">
                            <FileText className="h-3 w-3 text-blue-400" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{file.split('/').pop()}</p>
                            <p className="text-xs text-muted-foreground truncate">{file}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div>
                  <h3 className="text-sm font-semibold mb-3 flex items-center gap-2">
                    <Settings className="h-4 w-4 text-primary" />
                    Session Info
                  </h3>
                  <div className="space-y-2">
                    <div className="p-3 rounded-lg bg-muted/30 border border-border/30">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-xs text-muted-foreground">Mode</span>
                        <span className="text-sm font-medium">{currentMode}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-muted-foreground">Messages</span>
                        <span className="text-sm font-medium">{messages.length}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Timeline Tab */}
            {sidebarActiveTab === 'timeline' && (
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-semibold mb-3 flex items-center gap-2">
                    <Clock className="h-4 w-4 text-primary" />
                    Session Timeline
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-start gap-3 p-3 rounded-lg bg-muted/30 border border-border/30">
                      <div className="p-1.5 rounded-full bg-green-100/10 border border-green-200/20">
                        <Activity className="h-3 w-3 text-green-400" />
                      </div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Session Started</p>
                        <p className="text-xs text-muted-foreground">Mode: {currentMode}</p>
                        <p className="text-xs text-muted-foreground">Just now</p>
                      </div>
                    </div>
                    {messages.length > 0 && (
                      <div className="flex items-start gap-3 p-3 rounded-lg bg-muted/30 border border-border/30">
                        <div className="p-1.5 rounded-full bg-blue-100/10 border border-blue-200/20">
                          <Terminal className="h-3 w-3 text-blue-400" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium">First Message</p>
                          <p className="text-xs text-muted-foreground">{messages.length} total messages</p>
                          <p className="text-xs text-muted-foreground">Recently</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Stats Tab */}
            {sidebarActiveTab === 'stats' && (
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-semibold mb-3 flex items-center gap-2">
                    <BarChart3 className="h-4 w-4 text-primary" />
                    Session Statistics
                  </h3>
                  <div className="grid grid-cols-2 gap-3">
                    <div className="p-3 rounded-lg bg-muted/30 border border-border/30 text-center">
                      <div className="text-lg font-bold text-primary">{messages.length}</div>
                      <div className="text-xs text-muted-foreground">Messages</div>
                    </div>
                    <div className="p-3 rounded-lg bg-muted/30 border border-border/30 text-center">
                      <div className="text-lg font-bold text-accent">{totalTokens.toLocaleString()}</div>
                      <div className="text-xs text-muted-foreground">Tokens</div>
                    </div>
                    <div className="p-3 rounded-lg bg-muted/30 border border-border/30 text-center">
                      <div className="text-lg font-bold text-green-500">{recentTools.length}</div>
                      <div className="text-xs text-muted-foreground">Tools Used</div>
                    </div>
                    <div className="p-3 rounded-lg bg-muted/30 border border-border/30 text-center">
                      <div className="text-lg font-bold text-blue-500">{currentFiles.length}</div>
                      <div className="text-xs text-muted-foreground">Files</div>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-sm font-semibold mb-3 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-primary" />
                    Usage Breakdown
                  </h3>
                  <div className="space-y-3">
                    <div className="p-3 rounded-lg bg-muted/30 border border-border/30">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium">Token Usage</span>
                        <span className="text-xs text-muted-foreground">
                          {totalTokens < 10000 ? 'Low' : totalTokens < 50000 ? 'Medium' : 'High'}
                        </span>
                      </div>
                      <div className="h-2 bg-border/50 rounded-full overflow-hidden">
                        <div
                          className={cn(
                            "h-full transition-all duration-300",
                            totalTokens < 10000 ? "bg-green-500" :
                            totalTokens < 50000 ? "bg-amber-500" : "bg-red-500"
                          )}
                          style={{
                            width: `${Math.min((totalTokens / 100000) * 100, 100)}%`
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar Footer */}
          <div className="p-4 border-t border-border/50">
            <div className="text-xs text-muted-foreground text-center">
              <div className="flex items-center justify-center gap-4 mb-2">
                <span>⌘⇧S Toggle</span>
                <span>⌘⇧N Navigate</span>
                <span>⌘⇧C Context</span>
              </div>
              <div>Session context and navigation tools</div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  const messagesList = (
    <div ref={messageVirtualizerRef} className="relative h-full">
      <div className="h-full max-w-4xl mx-auto px-6 py-8">
        <MessageVirtualizer
          messages={messages}
          currentMode={currentMode}
          sessionId={effectiveSession?.id}
          isLoading={isLoading}
          showFollowUpQuestions={showFollowUpQuestions}
          recentTools={recentTools}
          currentFiles={currentFiles}
          onLinkDetected={handleLinkDetected}
          onFollowUpSelect={(question: FollowUpQuestion) => {
            setShowFollowUpQuestions(false);
            if (question.prompt) {
              floatingPromptRef.current?.setValue(question.prompt);
              handleSendPrompt(question.prompt, 'sonnet');
            }
          }}
          onFollowUpDismiss={() => setShowFollowUpQuestions(false)}
        />
      </div>
    </div>
  );
  
  const errorDisplay = error && (
    <div className="max-w-4xl mx-auto px-6 py-4">
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className="relative rounded-xl border border-destructive/50 bg-gradient-to-r from-destructive/10 to-red-500/10 p-6 lg:p-8 backdrop-blur-sm"
      >
      {/* Error icon and content */}
      <div className="flex items-start gap-4">
        <div className="flex-shrink-0 p-2 rounded-lg bg-destructive/20 border border-destructive/30">
          <X className="h-5 w-5 text-destructive" />
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="font-semibold text-destructive mb-1">Error</h4>
          <p className="text-sm text-destructive/80 leading-relaxed">{error}</p>
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setError(null)}
          className="h-8 w-8 text-destructive/60 hover:text-destructive hover:bg-destructive/10"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
      </motion.div>
    </div>
  );
  
  const loadingIndicator = isLoading && (
    <div className="max-w-4xl mx-auto px-6 py-12">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="flex flex-col items-center justify-center py-8"
      >
      <div className="relative">
        <div className="rotating-symbol text-primary text-lg" />
        {/* Pulsing backdrop */}
        <div className="absolute inset-0 rounded-full bg-primary/20 animate-ping" />
      </div>
      <div className="mt-4 text-center">
        <p className="text-sm font-medium text-foreground">
          {session ? "Loading session history..." : "Initializing Claude Code..."}
        </p>
        <p className="text-xs text-muted-foreground mt-1">
          This may take a few moments
        </p>
      </div>
      </motion.div>
    </div>
  );

  const projectPathInput = !session && (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1, duration: 0.6, ease: "easeOut" }}
      className="px-4 lg:px-8 py-6 lg:py-8 border-b border-border/50 flex-shrink-0"
    >
      <div className="max-w-4xl mx-auto">
        <div className="relative bg-gradient-to-r from-primary/5 to-accent/5 rounded-xl border border-border/50 p-4 lg:p-6 hover:shadow-lg hover:border-border/70 transition-all duration-300 group">
          {/* Decorative background pattern */}
          <div className="absolute inset-0 rounded-xl opacity-50 bg-[radial-gradient(circle_at_20%_20%,rgba(120,119,198,0.05),transparent_40%)]" />
          
          <div className="relative">
            <div className="flex items-start gap-3 lg:gap-4 mb-4">
              <div className="relative flex-shrink-0">
                <div className="p-2.5 rounded-xl bg-primary/10 border border-primary/20 group-hover:bg-primary/15 transition-colors">
                  <FolderOpen className="h-5 w-5 text-primary" />
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <Label htmlFor="project-path" className="text-lg lg:text-xl font-semibold text-foreground tracking-tight">
                  Project Directory
                </Label>
                <p className="text-sm text-muted-foreground mt-2 leading-relaxed">
                  {projectPath 
                    ? "Project ready. You can now start your Claude Code session."
                    : "Select your project folder to get started with intelligent code assistance"
                  }
                </p>
                
                {/* Quick tips */}
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
                <div className="relative flex-1">
                  <Input
                    id="project-path"
                    value={projectPath}
                    onChange={(e) => setProjectPath(e.target.value)}
                    placeholder="/path/to/your/project"
                    className={cn(
                      "h-12 text-sm rounded-lg border-input focus:border-primary/50 transition-all duration-200 pr-10 px-4",
                      "bg-background/50 backdrop-blur-sm font-mono",
                      projectPath && "border-green-500/30 bg-green-50/5"
                    )}
                    disabled={isLoading}
                  />
                </div>
                
                <Button
                  onClick={handleSelectPath}
                  size="lg"
                  variant="outline"
                  disabled={isLoading}
                  className="h-12 px-6 rounded-lg hover:bg-accent/20 hover:border-accent/50 transition-all duration-200 group/btn text-sm font-medium"
                >
                  <FolderOpen className="h-4 w-4 mr-2 group-hover/btn:scale-110 transition-transform" />
                  <span className="font-medium">Browse</span>
                </Button>
              </div>
              
              {/* Recently used projects hint */}
              {!projectPath && (
                <div className="text-xs text-muted-foreground flex items-center gap-2">
                  <div className="h-px flex-1 bg-border/50" />
                  <span>Tip: Drag & drop a folder here to select it quickly</span>
                  <div className="h-px flex-1 bg-border/50" />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );

  // If preview is maximized, render only the WebviewPreview in full screen
  if (showPreview && isPreviewMaximized) {
    return (
      <AnimatePresence>
        <motion.div 
          className="fixed inset-0 z-50 bg-background"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          <WebviewPreview
            initialUrl={previewUrl}
            onClose={handleClosePreview}
            isMaximized={isPreviewMaximized}
            onToggleMaximize={handleTogglePreviewMaximize}
            onUrlChange={handlePreviewUrlChange}
            className="h-full"
          />
        </motion.div>
      </AnimatePresence>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      <div className="w-full h-full flex flex-col">
        {/* Enhanced Header with improved design and UX */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="relative overflow-hidden"
        >
          {/* Enhanced background with subtle pattern */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-accent/5 to-primary/5" />
          <div className="absolute inset-0 opacity-30 bg-[radial-gradient(circle_at_50%_0%,rgba(120,119,198,0.1),transparent_50%)]" />
          
          <div className="relative flex items-center justify-between p-2 lg:p-3 border-b border-border/50">
            {/* Left Section - Navigation & Session Info */}
            <div className="flex items-center space-x-2 lg:space-x-3 flex-1 min-w-0">
              <Button
                variant="ghost"
                size="icon"
                onClick={onBack}
                className="h-7 w-7 lg:h-8 lg:w-8 rounded-lg hover:bg-accent/20 transition-all duration-200 flex-shrink-0"
                aria-label="Go back to projects"
              >
                <ArrowLeft className="h-3.5 w-3.5 lg:h-4 lg:w-4" />
              </Button>
              
              <div className="flex items-center space-x-2 min-w-0 flex-1">
                {/* Session Icon with enhanced visual */}
                <div className="relative flex-shrink-0">
                  <div className="p-1.5 rounded-lg bg-gradient-to-br from-primary/20 to-accent/20 border border-primary/20 shadow-sm">
                    <Terminal className="h-4 w-4 lg:h-4.5 lg:w-4.5 text-primary" />
                  </div>
                </div>
                
                {/* Session Title & Context */}
                <div className="min-w-0 flex-1">
                  <div className="flex items-center gap-2">
                    <h1 className="text-base lg:text-lg font-bold text-foreground tracking-tight truncate">
                      Claude Code Session
                    </h1>
                  </div>
                  
                  {/* Project path with enhanced styling */}
                  <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                    <Hash className="h-3 w-3 flex-shrink-0" />
                    <span className="truncate font-mono text-xs">
                      {projectPath && typeof projectPath === 'string' ? projectPath.split('/').pop() || projectPath : "No project selected"}
                    </span>
                    {projectPath && typeof projectPath === 'string' && (
                      <span className="hidden lg:inline text-muted-foreground/70 text-xs">
                        {projectPath}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              
            </div>
            
            {/* Right Section - Actions */}
            <div className="flex items-center gap-2 lg:gap-3 ml-3 flex-shrink-0">
              
              

            </div>
          </div>
        </motion.div>

        {/* Main Content Area */}
        <div className="flex-1 overflow-hidden">
          {showPreview ? (
            // Split pane layout when preview is active
            <SplitPane
              left={
                <div className="h-full flex flex-col">
                  {projectPathInput}
                  <div className="flex flex-1">
                    <div className="flex-1">
                      {messagesList}
                    </div>
                    
                  </div>
                </div>
              }
              right={
                <WebviewPreview
                  initialUrl={previewUrl}
                  onClose={handleClosePreview}
                  isMaximized={isPreviewMaximized}
                  onToggleMaximize={handleTogglePreviewMaximize}
                  onUrlChange={handlePreviewUrlChange}
                />
              }
              initialSplit={splitPosition}
              onSplitChange={setSplitPosition}
              minLeftWidth={400}
              minRightWidth={400}
              className="h-full"
            />
          ) : (
            // Original layout when no preview - Enhanced with better grid system
            <div className="h-full flex flex-col w-full">
              <div className="w-full max-w-7xl mx-auto px-4 lg:px-8 h-full flex flex-col">
              {projectPathInput}
              
              
              <div className="flex h-full">
                <div className="flex-1">
                  {messagesList}
                  {errorDisplay}
                </div>
                
              </div>
              
              {isLoading && messages.length === 0 && (
                <div className="flex items-center justify-center h-full">
                  <div className="flex items-center gap-3">
                    <div className="rotating-symbol text-primary" />
                    <span className="text-sm text-muted-foreground">
                      {session ? "Loading session history..." : "Initializing Claude Code..."}
                    </span>
                  </div>
                </div>
              )}
              
              {loadingIndicator}
              </div>
            </div>
          )}
        </div>

        {/* Floating Prompt Input - Always visible */}
        <ErrorBoundary>
          {/* Enhanced Queued Prompts Display */}
          <AnimatePresence>
            {queuedPrompts.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 20, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 20, scale: 0.95 }}
                className="fixed bottom-24 left-1/2 -translate-x-1/2 z-30 w-full max-w-4xl px-6"
              >
                <div className="relative bg-background/95 backdrop-blur-md border border-border/50 rounded-xl shadow-xl p-6 space-y-4">
                  {/* Header with enhanced design */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-1.5 rounded-lg bg-accent/10 border border-accent/20">
                        <Hash className="h-3.5 w-3.5 text-accent" />
                      </div>
                      <div>
                        <h4 className="text-sm font-semibold text-foreground">
                          Queued Prompts
                        </h4>
                        <p className="text-xs text-muted-foreground">
                          {queuedPrompts.length} prompt{queuedPrompts.length !== 1 ? 's' : ''} waiting
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => setQueuedPrompts([])}
                        className="text-xs px-2 py-1 h-auto text-muted-foreground hover:text-destructive"
                      >
                        Clear all
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => setQueuedPromptsCollapsed(!queuedPromptsCollapsed)}
                        className="h-7 w-7"
                      >
                        {queuedPromptsCollapsed ? <ChevronUp className="h-3.5 w-3.5" /> : <ChevronDown className="h-3.5 w-3.5" />}
                      </Button>
                    </div>
                  </div>
                  
                  {/* Queue items with enhanced styling */}
                  <AnimatePresence>
                    {!queuedPromptsCollapsed && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        exit={{ opacity: 0, height: 0 }}
                        className="space-y-2 max-h-64 overflow-y-auto"
                      >
                        {queuedPrompts.map((queuedPrompt, index) => (
                          <motion.div
                            key={queuedPrompt.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: 20 }}
                            transition={{ delay: index * 0.05 }}
                            className="group flex items-start gap-3 bg-muted/30 hover:bg-muted/50 rounded-lg p-3 border border-border/30 hover:border-border/50 transition-all duration-200"
                          >
                            {/* Queue position indicator */}
                            <div className="flex-shrink-0 flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 border border-primary/20 text-xs font-medium text-primary">
                              {index + 1}
                            </div>
                            
                            <div className="flex-1 min-w-0 space-y-2">
                              {/* Model badge and metadata */}
                              <div className="flex items-center gap-2">
                                <span className={cn(
                                  "text-xs px-2 py-0.5 rounded-full font-medium border",
                                  queuedPrompt.model === "opus" 
                                    ? "bg-purple-100/10 border-purple-200/20 text-purple-400"
                                    : "bg-blue-100/10 border-blue-200/20 text-blue-400"
                                )}>
                                  {queuedPrompt.model === "opus" ? "Opus" : "Sonnet"}
                                </span>
                                <span className="text-xs text-muted-foreground">
                                  • {queuedPrompt.prompt.length} chars
                                </span>
                              </div>
                              
                              {/* Prompt preview */}
                              <p className="text-sm text-foreground/90 line-clamp-2 break-words leading-relaxed">
                                {queuedPrompt.prompt}
                              </p>
                            </div>
                            
                            {/* Remove button */}
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-7 w-7 flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity text-muted-foreground hover:text-destructive hover:bg-destructive/10"
                              onClick={() => setQueuedPrompts(prev => prev.filter(p => p.id !== queuedPrompt.id))}
                            >
                              <X className="h-3.5 w-3.5" />
                            </Button>
                          </motion.div>
                        ))}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Enhanced Navigation Arrows with Smart State Management */}
          <AnimatePresence>
            {messages.length > 5 && (canScrollUp || canScrollDown) && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8, x: 20 }}
                animate={{ opacity: 1, scale: 1, x: 0 }}
                exit={{ opacity: 0, scale: 0.8, x: 20 }}
                transition={{ duration: 0.3 }}
                className="fixed bottom-32 right-6 z-50"
              >
                <div className="flex flex-col bg-background/95 backdrop-blur-md border border-border/50 rounded-xl shadow-xl overflow-hidden">
                  {/* Scroll Position Indicator */}
                  <div className="px-3 py-2 border-b border-border/30">
                    <div className="flex items-center gap-2">
                      <div className="flex flex-col gap-1">
                        <div className={cn(
                          "h-1 w-4 rounded-full transition-colors",
                          scrollPosition === 'top' ? "bg-primary" : "bg-muted"
                        )} />
                        <div className={cn(
                          "h-1 w-4 rounded-full transition-colors",
                          scrollPosition === 'middle' ? "bg-primary" : "bg-muted"
                        )} />
                        <div className={cn(
                          "h-1 w-4 rounded-full transition-colors",
                          scrollPosition === 'bottom' ? "bg-primary" : "bg-muted"
                        )} />
                      </div>
                      <div className="text-xs font-medium text-muted-foreground">
                        {messages.length}
                      </div>
                    </div>
                  </div>
                  
                  {/* Navigation Buttons */}
                  <div className="flex flex-col">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleScrollToTop}
                      disabled={!canScrollUp}
                      className={cn(
                        "px-3 py-2 rounded-none transition-all duration-200",
                        canScrollUp 
                          ? "hover:bg-accent hover:text-accent-foreground" 
                          : "opacity-50 cursor-not-allowed"
                      )}
                      title="Scroll to top"
                    >
                      <ChevronUp className="h-4 w-4" />
                    </Button>
                    
                    <div className="w-full h-px bg-border/30" />
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleScrollToBottom}
                      disabled={!canScrollDown}
                      className={cn(
                        "px-3 py-2 rounded-none transition-all duration-200",
                        canScrollDown 
                          ? "hover:bg-accent hover:text-accent-foreground" 
                          : "opacity-50 cursor-not-allowed"
                      )}
                      title="Scroll to bottom"
                    >
                      <ChevronDown className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Mode Suggestion Banner */}
          {modeSuggestion && (
            <div className="fixed bottom-24 left-0 right-0 transition-all duration-300 z-50 px-4">
              <div className="max-w-3xl mx-auto">
                <ModeSuggestionBanner
                  suggestion={modeSuggestion}
                  onAccept={handleAcceptModeSuggestion}
                  onDismiss={handleDismissModeSuggestion}
                />
              </div>
            </div>
          )}

          <div className="fixed bottom-0 left-0 right-0 transition-all duration-300 z-40">
            <FloatingPromptInput
              ref={floatingPromptRef}
              onSend={handleSendPrompt}
              onCancel={handleCancelExecution}
              isLoading={isLoading}
              disabled={!projectPath}
              projectPath={projectPath}
            />
          </div>

          {/* Enhanced Token Counter */}
          {totalTokens > 0 && (
            <div className="fixed bottom-0 left-0 right-0 z-30 pointer-events-none">
              <div className="max-w-5xl mx-auto">
                <div className="flex justify-end px-4 pb-2">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.8, y: 10 }}
                    className="bg-background/95 backdrop-blur-md border border-border/50 rounded-full px-4 py-2 shadow-lg pointer-events-auto group hover:shadow-xl transition-all duration-200"
                  >
                    <div className="flex items-center gap-2 text-xs">
                      <div className="p-1 rounded-full bg-primary/10 border border-primary/20 group-hover:bg-primary/15 transition-colors">
                        <Hash className="h-3 w-3 text-primary" />
                      </div>
                      <div className="flex items-center gap-1.5">
                        <span className="font-mono font-semibold text-foreground">
                          {totalTokens.toLocaleString()}
                        </span>
                        <span className="text-muted-foreground font-medium">tokens</span>
                      </div>
                      {/* Usage indicator */}
                      <div className="w-px h-3 bg-border/50" />
                      <div className={cn(
                        "h-2 w-2 rounded-full transition-colors",
                        totalTokens < 10000 ? "bg-green-500" :
                        totalTokens < 50000 ? "bg-amber-500" : "bg-red-500"
                      )} />
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          )}
        </ErrorBoundary>

      </div>

      {/* Session Context Sidebar */}
      <SessionContextSidebar />

      {/* Sidebar Overlay for Mobile */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-30 lg:hidden"
            onClick={handleToggleSidebar}
          />
        )}
      </AnimatePresence>

      {/* Mode Transition Dialog */}
      {showModeTransitionDialog && pendingModeTransition && effectiveSession && (
        <ModeTransitionDialog
          isOpen={showModeTransitionDialog}
          onClose={() => {
            setShowModeTransitionDialog(false);
            setPendingModeTransition(null);
          }}
          onConfirm={async (reason) => {
            try {
              await switchSessionMode(
                effectiveSession.id,
                effectiveSession.project_id,
                pendingModeTransition.to,
                reason
              );
              setCurrentMode(pendingModeTransition.to);
              setShowModeTransitionDialog(false);
              setPendingModeTransition(null);
            } catch (error) {
              console.error('Failed to switch mode:', error);
              setError('Failed to switch mode');
            }
          }}
          fromMode={pendingModeTransition.from}
          toMode={pendingModeTransition.to}
          sessionHasUnsavedChanges={messages.length > 0 && isLoading}
        />
      )}
    </div>
  );
};
