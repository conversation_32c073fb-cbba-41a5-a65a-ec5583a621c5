import React, { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  <PERSON>older<PERSON><PERSON>, 
  FileText, 
  Settings,
  MoreVertical,
  Search,
  Filter,
  Grid3X3,
  List,
  Star,
  Clock,
  Code2,
  GitBranch,
  Activity,
  Sparkles,
  ArrowUpRight,
  FolderGit2,
  Plus,
  Zap,
  Brain,
  Info,
  Archive,
  TrendingUp,
  Eye,
  ChevronDown
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { Project } from "@/lib/api";
import { cn } from "@/lib/utils";
import { formatTimeAgo } from "@/lib/date-utils";
import { Pagination } from "@/components/ui/pagination";

interface ProjectListDesignedProps {
  projects: Project[];
  onProjectClick: (project: Project) => void;
  onProjectSettings?: (project: Project) => void;
  onCreateProject?: () => void;
  loading?: boolean;
  className?: string;
}

type ViewMode = "grid" | "list";
type SortOption = "recent" | "name" | "sessions" | "activity";
type FilterOption = "all" | "active" | "recent" | "archived";

const ITEMS_PER_PAGE = 12;

/**
 * Extracts the project name from the full path
 */
const getProjectName = (path: string): string => {
  const parts = path.split('/').filter(Boolean);
  return parts[parts.length - 1] || path;
};

/**
 * Get project status based on activity
 */
const getProjectStatus = (project: Project): { status: string; color: string; icon: React.ReactNode } => {
  const daysSinceCreated = (Date.now() - project.created_at * 1000) / (1000 * 60 * 60 * 24);
  const hasRecentSessions = project.sessions.length > 0;

  if (hasRecentSessions && daysSinceCreated <= 7) {
    return { 
      status: "Active", 
      color: "bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300",
      icon: <Activity className="h-3 w-3" />
    };
  } else if (daysSinceCreated <= 7) {
    return { 
      status: "Recent", 
      color: "bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300",
      icon: <Clock className="h-3 w-3" />
    };
  } else if (daysSinceCreated > 30) {
    return { 
      status: "Archived", 
      color: "bg-gray-100 dark:bg-gray-900/20 text-gray-700 dark:text-gray-300",
      icon: <Archive className="h-3 w-3" />
    };
  } else {
    return { 
      status: "Idle", 
      color: "bg-yellow-100 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300",
      icon: <Clock className="h-3 w-3" />
    };
  }
};

/**
 * Enhanced ProjectList with CreateAgent-inspired design
 */
export const ProjectListDesigned: React.FC<ProjectListDesignedProps> = ({
  projects,
  onProjectClick,
  onProjectSettings,
  onCreateProject,
  loading,
  className,
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [sortBy, setSortBy] = useState<SortOption>("recent");
  const [filterBy, setFilterBy] = useState<FilterOption>("all");
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  
  // Filter and sort projects
  const filteredProjects = useMemo(() => {
    let filtered = projects.filter(project => {
      const name = getProjectName(project.path).toLowerCase();
      const path = project.path.toLowerCase();
      const query = searchQuery.toLowerCase();
      const matchesSearch = name.includes(query) || path.includes(query);
      
      if (!matchesSearch) return false;
      
      // Apply status filter
      if (filterBy !== "all") {
        const { status } = getProjectStatus(project);
        if (filterBy === "active" && status !== "Active") return false;
        if (filterBy === "recent" && status !== "Recent") return false;
        if (filterBy === "archived" && status !== "Archived") return false;
      }
      
      return true;
    });

    // Sort projects
    filtered.sort((a, b) => {
      // Favorites always come first
      const aFav = favorites.has(a.id);
      const bFav = favorites.has(b.id);
      if (aFav && !bFav) return -1;
      if (!aFav && bFav) return 1;

      switch (sortBy) {
        case "name":
          return getProjectName(a.path).localeCompare(getProjectName(b.path));
        case "sessions":
          return b.sessions.length - a.sessions.length;
        case "activity":
          // For activity sorting, use creation time as proxy since sessions are just IDs
          return b.created_at - a.created_at;
        case "recent":
        default:
          return b.created_at - a.created_at;
      }
    });

    return filtered;
  }, [projects, searchQuery, sortBy, filterBy, favorites]);
  
  // Calculate pagination
  const totalPages = Math.ceil(filteredProjects.length / ITEMS_PER_PAGE);
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;
  const currentProjects = filteredProjects.slice(startIndex, endIndex);
  
  // Reset to page 1 when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, sortBy, filterBy]);

  const toggleFavorite = (projectId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(projectId)) {
        newFavorites.delete(projectId);
      } else {
        newFavorites.add(projectId);
      }
      return newFavorites;
    });
  };

  const ProjectCard = ({ project, index }: { project: Project; index: number }) => {
    const isFavorite = favorites.has(project.id);
    const projectName = getProjectName(project.path);
    const { status, color, icon } = getProjectStatus(project);
    const sessionCount = project.sessions.length;
    const isPopular = sessionCount >= 5;

    return (
      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9 }}
        transition={{
          duration: 0.3,
          delay: index * 0.03,
          ease: [0.4, 0, 0.2, 1],
        }}
        className={cn(
          viewMode === "grid" && "h-full"
        )}
      >
        <Card 
          variant="enhanced"
          className={cn(
            "group cursor-pointer overflow-hidden transition-all duration-300",
            "hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-1",
            "border-border/50 hover:border-primary/30",
            viewMode === "grid" ? "h-full" : "flex items-center p-4",
            isFavorite && "ring-2 ring-yellow-500/20 border-yellow-500/30"
          )}
          onClick={() => onProjectClick(project)}
        >
          {viewMode === "grid" ? (
            <>
              {/* Card Header with Gradient - Compact */}
              <CardHeader className="bg-gradient-to-r from-primary/5 to-accent/5 border-b border-border/50 pb-2 p-4">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <div className="p-1.5 rounded-lg bg-primary/10 border border-primary/20 group-hover:bg-primary/20 transition-colors">
                      <FolderGit2 className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-base font-semibold truncate group-hover:text-primary transition-colors">
                        {projectName}
                      </CardTitle>
                      <CardDescription className="text-xs font-mono truncate mt-0.5">
                        {project.path}
                      </CardDescription>
                    </div>
                  </div>
                  
                  {/* Favorite Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-6 w-6 p-0 opacity-0 group-hover:opacity-100",
                      "transition-all duration-200 hover:bg-yellow-100 dark:hover:bg-yellow-900/20",
                      isFavorite && "opacity-100 text-yellow-500"
                    )}
                    onClick={(e) => toggleFavorite(project.id, e)}
                  >
                    <Star className={cn(
                      "h-3 w-3",
                      isFavorite && "fill-current"
                    )} />
                  </Button>
                </div>
                
                {/* Status and Popular Badges */}
                <div className="flex items-center gap-1.5 mt-2">
                  <Badge className={cn("text-xs px-1.5 py-0.5", color)}>
                    {icon}
                    <span className="ml-1">{status}</span>
                  </Badge>
                  {isPopular && (
                    <Badge className="text-xs px-1.5 py-0.5 bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300">
                      <TrendingUp className="h-2.5 w-2.5 mr-1" />
                      Popular
                    </Badge>
                  )}
                </div>
              </CardHeader>
              
              {/* Card Content - Compact */}
              <CardContent className="p-4">
                <div className="space-y-3">
                  {/* Stats Grid - Compact */}
                  <div className="grid grid-cols-2 gap-3">
                    <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      <span>{formatTimeAgo(project.created_at * 1000)}</span>
                    </div>
                    <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                      <FileText className="h-3 w-3" />
                      <span>{sessionCount} sessions</span>
                    </div>
                  </div>
                  
                  {/* Session Activity Indicators - Compact */}
                  {sessionCount > 0 && (
                    <div className="space-y-1.5">
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>Activity</span>
                        <span>{Math.min(sessionCount, 5)} of {sessionCount}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(sessionCount, 5) }).map((_, idx) => (
                          <div
                            key={idx}
                            className={cn(
                              "h-1.5 rounded-full transition-all duration-300",
                              "bg-gradient-to-r from-primary/60 to-primary/40",
                              "group-hover:from-primary/80 group-hover:to-primary/60",
                              idx === 0 && "w-6",
                              idx === 1 && "w-5",
                              idx === 2 && "w-4",
                              idx === 3 && "w-3",
                              idx === 4 && "w-2"
                            )}
                          />
                        ))}
                        {sessionCount > 5 && (
                          <span className="text-xs text-muted-foreground ml-1.5">
                            +{sessionCount - 5}
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Action Buttons - Compact */}
                  <div className="flex items-center justify-between pt-1.5 border-t border-border/50">
                    <div className="flex items-center gap-2">
                      {status === "Active" && (
                        <div className="flex items-center gap-1 text-xs text-green-600 dark:text-green-400">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse" />
                          <span>Live</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      {onProjectSettings && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0"
                          onClick={(e) => {
                            e.stopPropagation();
                            onProjectSettings(project);
                          }}
                        >
                          <Settings className="h-3 w-3" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                      >
                        <ArrowUpRight className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </>
          ) : (
            /* List View */
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-4 flex-1 min-w-0">
                <div className="p-2 rounded-lg bg-primary/10 border border-primary/20">
                  <FolderGit2 className="h-5 w-5 text-primary" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-base truncate group-hover:text-primary transition-colors">
                      {projectName}
                    </h3>
                    <Badge className={cn("text-xs px-2 py-0.5", color)}>
                      {icon}
                      <span className="ml-1">{status}</span>
                    </Badge>
                    {isPopular && (
                      <Badge className="text-xs px-2 py-0.5 bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        Popular
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground font-mono truncate">
                    {project.path}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>{formatTimeAgo(project.created_at * 1000)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  <span>{sessionCount} sessions</span>
                </div>
                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "h-8 w-8 p-0",
                      isFavorite && "text-yellow-500"
                    )}
                    onClick={(e) => toggleFavorite(project.id, e)}
                  >
                    <Star className={cn(
                      "h-4 w-4",
                      isFavorite && "fill-current"
                    )} />
                  </Button>
                  {onProjectSettings && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      onClick={(e) => {
                        e.stopPropagation();
                        onProjectSettings(project);
                      }}
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                  >
                    <ArrowUpRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          )}
        </Card>
      </motion.div>
    );
  };

  return (
    <div className={cn("flex flex-col h-full bg-background", className)}>
      <div className="w-full max-w-7xl mx-auto flex flex-col h-full">
        {/* Enhanced Header - Inspired by CreateAgent */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className="relative overflow-hidden"
        >
          {/* Background gradient */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-accent/5 to-primary/5" />
          
          <div className="relative p-6 border-b border-border/50">
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-xl bg-gradient-to-br from-primary/20 to-accent/20 border border-primary/20">
                  <FolderOpen className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                    Projects
                  </h1>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <Zap className="h-3 w-3" />
                    Manage your Claude Code projects
                  </p>
                </div>
              </div>
              
              {onCreateProject && (
                <Button
                  onClick={onCreateProject}
                  className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg hover:shadow-xl transition-all"
                  size="lg"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  New Project
                </Button>
              )}
            </div>
            
            {/* Enhanced Controls */}
            <div className="space-y-4">
              {/* Search and Filters */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search projects..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 h-11 rounded-xl border-input focus:border-primary/50 transition-colors"
                  />
                </div>
                
                <div className="flex gap-2">
                  <Select value={filterBy} onValueChange={(value: FilterOption) => setFilterBy(value)}>
                    <SelectTrigger className="w-[140px] h-11 rounded-xl">
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Projects</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="recent">Recent</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
                    <SelectTrigger className="w-[140px] h-11 rounded-xl">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="recent">Recent</SelectItem>
                      <SelectItem value="name">Name</SelectItem>
                      <SelectItem value="sessions">Sessions</SelectItem>
                      <SelectItem value="activity">Activity</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              {/* View Toggle and Stats */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-6 text-sm text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-4 w-4" />
                    <span>{filteredProjects.length} projects</span>
                  </div>
                  {favorites.size > 0 && (
                    <div className="flex items-center gap-2">
                      <Star className="h-4 w-4 fill-yellow-500 text-yellow-500" />
                      <span>{favorites.size} favorites</span>
                    </div>
                  )}
                  {filteredProjects.filter(p => getProjectStatus(p).status === "Active").length > 0 && (
                    <div className="flex items-center gap-2">
                      <Activity className="h-4 w-4 text-green-500" />
                      <span>{filteredProjects.filter(p => getProjectStatus(p).status === "Active").length} active</span>
                    </div>
                  )}
                </div>
                
                <div className="flex items-center gap-1 p-1 bg-muted rounded-lg">
                  <Button
                    variant={viewMode === "grid" ? "secondary" : "ghost"}
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => setViewMode("grid")}
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "secondary" : "ghost"}
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => setViewMode("list")}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
        
        {/* Project Grid/List */}
        <div className="flex-1 overflow-y-auto px-6 py-8">
          {loading ? (
            <div className={cn(
              viewMode === "grid"
                ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
                : "space-y-4"
            )}>
              {[...Array(6)].map((_, i) => (
                <div key={i} className={cn(
                  "rounded-xl bg-muted animate-pulse",
                  viewMode === "grid" ? "h-[220px]" : "h-[80px]"
                )} />
              ))}
            </div>
          ) : (
            <>
              <AnimatePresence mode="popLayout">
                <div className={cn(
                  viewMode === "grid"
                    ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
                    : "space-y-3"
                )}>
                  {currentProjects.map((project, index) => (
                    <ProjectCard 
                      key={project.id} 
                      project={project} 
                      index={index} 
                    />
                  ))}
                </div>
              </AnimatePresence>

              {filteredProjects.length === 0 && (
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex flex-col items-center justify-center py-20"
                >
                  <div className="p-4 rounded-2xl bg-gradient-to-br from-primary/10 to-accent/10 border border-primary/20 mb-6">
                    <FolderOpen className="h-12 w-12 text-primary" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">
                    {searchQuery || filterBy !== "all" 
                      ? "No projects found" 
                      : "No projects yet"
                    }
                  </h3>
                  <p className="text-muted-foreground text-center mb-6 max-w-md">
                    {searchQuery || filterBy !== "all"
                      ? "Try adjusting your search or filter criteria"
                      : "Create your first project to start building with Claude Code"
                    }
                  </p>
                  {onCreateProject && !searchQuery && filterBy === "all" && (
                    <Button 
                      onClick={onCreateProject}
                      className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Create Your First Project
                    </Button>
                  )}
                </motion.div>
              )}
            </>
          )}
          
          {/* Pagination */}
          {totalPages > 1 && (
            <div className="mt-8">
              <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={setCurrentPage}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};