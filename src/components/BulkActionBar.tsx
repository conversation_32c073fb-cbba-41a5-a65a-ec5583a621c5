import React from 'react';
import { 
  X, 
  Trash2, 
  UserX,
  ArrowUpDown,
  FileDown,
  Copy,
  Ban,
  AlertCircle
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { cn } from '@/lib/utils';

interface BulkActionBarProps {
  type: 'agent' | 'task';
  selectedCount: number;
  totalCount: number;
  onClearSelection: () => void;
  onBulkAction: (action: string, parameters?: Record<string, any>) => void;
  onSelectAll: () => void;
  className?: string;
  availableAgents?: Array<{ id: string; name: string }>;
}

export function BulkActionBar({
  type,
  selectedCount,
  totalCount,
  onClearSelection,
  onBulkAction,
  onSelectAll,
  className,
  availableAgents = [],
}: BulkActionBarProps) {
  const [showPrioritySlider, setShowPrioritySlider] = React.useState(false);
  const [priority, setPriority] = React.useState([5]);
  const [selectedAgentId, setSelectedAgentId] = React.useState<string>('');

  const handlePriorityChange = () => {
    onBulkAction('changePriority', { priority: priority[0] });
    setShowPrioritySlider(false);
    setPriority([5]);
  };

  const handleReassign = () => {
    if (selectedAgentId) {
      onBulkAction('reassign', { agentId: selectedAgentId });
      setSelectedAgentId('');
    }
  };

  const renderAgentActions = () => (
    <>
      <Button
        size="sm"
        variant="destructive"
        onClick={() => onBulkAction('terminate')}
        className="gap-2"
      >
        <UserX className="h-4 w-4" />
        Terminate
      </Button>

      <div className="flex items-center gap-2">
        {showPrioritySlider ? (
          <>
            <div className="w-32">
              <Slider
                value={priority}
                onValueChange={setPriority}
                min={1}
                max={10}
                step={1}
                className="w-full"
              />
            </div>
            <span className="text-sm text-muted-foreground w-8">{priority[0]}</span>
            <Button size="sm" variant="secondary" onClick={handlePriorityChange}>
              Apply
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => {
                setShowPrioritySlider(false);
                setPriority([5]);
              }}
            >
              Cancel
            </Button>
          </>
        ) : (
          <Button
            size="sm"
            variant="outline"
            onClick={() => setShowPrioritySlider(true)}
            className="gap-2"
          >
            <ArrowUpDown className="h-4 w-4" />
            Change Priority
          </Button>
        )}
      </div>

      <Button
        size="sm"
        variant="outline"
        onClick={() => onBulkAction('export')}
        className="gap-2"
      >
        <FileDown className="h-4 w-4" />
        Export
      </Button>

      <Button
        size="sm"
        variant="outline"
        onClick={() => onBulkAction('duplicate')}
        className="gap-2"
      >
        <Copy className="h-4 w-4" />
        Duplicate
      </Button>
    </>
  );

  const renderTaskActions = () => (
    <>
      <Button
        size="sm"
        variant="destructive"
        onClick={() => onBulkAction('cancel')}
        className="gap-2"
      >
        <Ban className="h-4 w-4" />
        Cancel
      </Button>

      {availableAgents.length > 0 && (
        <div className="flex items-center gap-2">
          <Select value={selectedAgentId} onValueChange={setSelectedAgentId}>
            <SelectTrigger className="w-40 h-8">
              <SelectValue placeholder="Reassign to..." />
            </SelectTrigger>
            <SelectContent>
              {availableAgents.map(agent => (
                <SelectItem key={agent.id} value={agent.id}>
                  {agent.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {selectedAgentId && (
            <Button size="sm" variant="secondary" onClick={handleReassign}>
              Reassign
            </Button>
          )}
        </div>
      )}

      <div className="flex items-center gap-2">
        {showPrioritySlider ? (
          <>
            <div className="w-32">
              <Slider
                value={priority}
                onValueChange={setPriority}
                min={1}
                max={10}
                step={1}
                className="w-full"
              />
            </div>
            <span className="text-sm text-muted-foreground w-8">{priority[0]}</span>
            <Button size="sm" variant="secondary" onClick={handlePriorityChange}>
              Apply
            </Button>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => {
                setShowPrioritySlider(false);
                setPriority([5]);
              }}
            >
              Cancel
            </Button>
          </>
        ) : (
          <Button
            size="sm"
            variant="outline"
            onClick={() => setShowPrioritySlider(true)}
            className="gap-2"
          >
            <ArrowUpDown className="h-4 w-4" />
            Change Priority
          </Button>
        )}
      </div>

      <Button
        size="sm"
        variant="outline"
        onClick={() => onBulkAction('delete')}
        className="gap-2"
      >
        <Trash2 className="h-4 w-4" />
        Delete
      </Button>

      <Button
        size="sm"
        variant="outline"
        onClick={() => onBulkAction('export')}
        className="gap-2"
      >
        <FileDown className="h-4 w-4" />
        Export
      </Button>

      <Button
        size="sm"
        variant="outline"
        onClick={() => onBulkAction('clone')}
        className="gap-2"
      >
        <Copy className="h-4 w-4" />
        Clone
      </Button>
    </>
  );

  if (selectedCount === 0) {
    return null;
  }

  return (
    <div
      className={cn(
        'fixed bottom-4 left-1/2 -translate-x-1/2 z-50',
        'bg-background border rounded-lg shadow-lg p-4',
        'flex items-center gap-4',
        'animate-in fade-in slide-in-from-bottom-2 duration-200',
        className
      )}
    >
      <div className="flex items-center gap-3">
        <Badge variant="secondary" className="gap-1">
          <AlertCircle className="h-3 w-3" />
          {selectedCount} {type}{selectedCount !== 1 ? 's' : ''} selected
        </Badge>

        {selectedCount < totalCount && (
          <Button
            size="sm"
            variant="ghost"
            onClick={onSelectAll}
            className="text-xs"
          >
            Select all {totalCount}
          </Button>
        )}
      </div>

      <div className="h-8 w-px bg-border" />

      <div className="flex items-center gap-2">
        {type === 'agent' ? renderAgentActions() : renderTaskActions()}
      </div>

      <div className="h-8 w-px bg-border" />

      <Button
        size="sm"
        variant="ghost"
        onClick={onClearSelection}
        className="gap-2"
      >
        <X className="h-4 w-4" />
        Clear
      </Button>
    </div>
  );
}