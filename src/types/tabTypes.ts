/**
 * Segregated tab interfaces following Interface Segregation Principle
 * Each tab type only includes the properties it actually needs
 */

/**
 * Base tab interface with common properties
 */
export interface BaseTab {
  id: string;
  title: string;
  status: 'active' | 'idle' | 'running' | 'complete' | 'error';
  hasUnsavedChanges: boolean;
  order: number;
  icon?: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Chat tab specific properties
 */
export interface ChatTabData {
  type: 'chat';
  sessionId?: string;
  sessionData?: any;
  initialProjectPath?: string;
  projectPath?: string;
  projectId?: string;
}

/**
 * Agent tab specific properties
 */
export interface AgentTabData {
  type: 'agent';
  agentRunId: string;
}

/**
 * Agent execution tab specific properties
 */
export interface AgentExecutionTabData {
  type: 'agent-execution';
  agentData: any;
}

/**
 * Claude file tab specific properties
 */
export interface ClaudeFileTabData {
  type: 'claude-file';
  claudeFileId: string;
}

/**
 * Project settings tab specific properties
 */
export interface ProjectSettingsTabData {
  type: 'project-settings';
  projectId: string;
  projectPath: string;
  projectData?: any;
}

/**
 * Simple tab types that only need base properties
 */
export interface SimpleTabData {
  type: 'projects' | 'mcp' | 'settings' | 'claude-md' | 'create-agent' | 'import-agent' | 'cc-agents' | 'claude-flow';
}

/**
 * Union type for all tab data types
 */
export type TabData = 
  | ChatTabData
  | AgentTabData
  | AgentExecutionTabData
  | ClaudeFileTabData
  | ProjectSettingsTabData
  | SimpleTabData;

/**
 * Complete tab type combining base and specific data
 */
export type Tab = BaseTab & TabData;

/**
 * Type-safe tab creation data
 */
export type CreateTabData<T extends TabData['type']> = 
  T extends 'chat' ? Omit<BaseTab, 'id' | 'order' | 'createdAt' | 'updatedAt'> & ChatTabData :
  T extends 'agent' ? Omit<BaseTab, 'id' | 'order' | 'createdAt' | 'updatedAt'> & AgentTabData :
  T extends 'agent-execution' ? Omit<BaseTab, 'id' | 'order' | 'createdAt' | 'updatedAt'> & AgentExecutionTabData :
  T extends 'claude-file' ? Omit<BaseTab, 'id' | 'order' | 'createdAt' | 'updatedAt'> & ClaudeFileTabData :
  T extends 'project-settings' ? Omit<BaseTab, 'id' | 'order' | 'createdAt' | 'updatedAt'> & ProjectSettingsTabData :
  Omit<BaseTab, 'id' | 'order' | 'createdAt' | 'updatedAt'> & SimpleTabData;

/**
 * Type guards for tab types
 */
export function isChatTab(tab: Tab): tab is BaseTab & ChatTabData {
  return tab.type === 'chat';
}

export function isAgentTab(tab: Tab): tab is BaseTab & AgentTabData {
  return tab.type === 'agent';
}

export function isAgentExecutionTab(tab: Tab): tab is BaseTab & AgentExecutionTabData {
  return tab.type === 'agent-execution';
}

export function isClaudeFileTab(tab: Tab): tab is BaseTab & ClaudeFileTabData {
  return tab.type === 'claude-file';
}

export function isProjectSettingsTab(tab: Tab): tab is BaseTab & ProjectSettingsTabData {
  return tab.type === 'project-settings';
}

export function isSimpleTab(tab: Tab): tab is BaseTab & SimpleTabData {
  return ['projects', 'mcp', 'settings', 'claude-md', 'create-agent', 'import-agent', 'cc-agents', 'crush', 'claude-flow'].includes(tab.type);
}

/**
 * Tab validation utilities
 */
export function validateTabData(tab: Tab): boolean {
  // Validate required fields based on tab type
  switch (tab.type) {
    case 'chat':
      // Chat tabs can work without sessionId for new sessions
      return true;
    
    case 'agent':
      return !!(tab as BaseTab & AgentTabData).agentRunId;
    
    case 'agent-execution':
      return !!(tab as BaseTab & AgentExecutionTabData).agentData;
    
    case 'claude-file':
      return !!(tab as BaseTab & ClaudeFileTabData).claudeFileId;
    
    default:
      return true;
  }
}

/**
 * Factory functions for creating tabs
 */
export const TabFactory = {
  createChatTab(data: Partial<ChatTabData> & Pick<BaseTab, 'title' | 'status' | 'hasUnsavedChanges'>): Omit<BaseTab & ChatTabData, 'id' | 'order' | 'createdAt' | 'updatedAt'> {
    return {
      type: 'chat',
      title: data.title,
      status: data.status,
      hasUnsavedChanges: data.hasUnsavedChanges,
      icon: data.icon || 'message-square',
      sessionId: data.sessionId,
      sessionData: data.sessionData,
      initialProjectPath: data.initialProjectPath,
      projectPath: data.projectPath,
      projectId: data.projectId,
    };
  },

  createAgentTab(data: Partial<AgentTabData> & Pick<BaseTab, 'title' | 'status' | 'hasUnsavedChanges'> & { agentRunId: string }): Omit<BaseTab & AgentTabData, 'id' | 'order' | 'createdAt' | 'updatedAt'> {
    return {
      type: 'agent',
      title: data.title,
      status: data.status,
      hasUnsavedChanges: data.hasUnsavedChanges,
      icon: data.icon || 'bot',
      agentRunId: data.agentRunId,
    };
  },

  createAgentExecutionTab(data: Partial<AgentExecutionTabData> & Pick<BaseTab, 'title' | 'status' | 'hasUnsavedChanges'> & { agentData: any }): Omit<BaseTab & AgentExecutionTabData, 'id' | 'order' | 'createdAt' | 'updatedAt'> {
    return {
      type: 'agent-execution',
      title: data.title,
      status: data.status,
      hasUnsavedChanges: data.hasUnsavedChanges,
      icon: data.icon || 'bot',
      agentData: data.agentData,
    };
  },

  createClaudeFileTab(data: Partial<ClaudeFileTabData> & Pick<BaseTab, 'title' | 'status' | 'hasUnsavedChanges'> & { claudeFileId: string }): Omit<BaseTab & ClaudeFileTabData, 'id' | 'order' | 'createdAt' | 'updatedAt'> {
    return {
      type: 'claude-file',
      title: data.title,
      status: data.status,
      hasUnsavedChanges: data.hasUnsavedChanges,
      icon: data.icon || 'file-text',
      claudeFileId: data.claudeFileId,
    };
  },

  createProjectSettingsTab(data: Partial<ProjectSettingsTabData> & Pick<BaseTab, 'title' | 'status' | 'hasUnsavedChanges'> & { projectId: string; projectPath: string; icon?: string }): Omit<BaseTab & ProjectSettingsTabData, 'id' | 'order' | 'createdAt' | 'updatedAt'> {
    return {
      type: 'project-settings',
      title: data.title,
      status: data.status,
      hasUnsavedChanges: data.hasUnsavedChanges,
      icon: data.icon || 'settings',
      projectId: data.projectId,
      projectPath: data.projectPath,
      projectData: data.projectData,
    };
  },

  createSimpleTab<T extends SimpleTabData['type']>(type: T, data: Pick<BaseTab, 'title' | 'status' | 'hasUnsavedChanges'> & { icon?: string }): Omit<BaseTab & SimpleTabData, 'id' | 'order' | 'createdAt' | 'updatedAt'> {
    return {
      type,
      title: data.title,
      status: data.status,
      hasUnsavedChanges: data.hasUnsavedChanges,
      icon: data.icon,
    };
  }
};

export default Tab;