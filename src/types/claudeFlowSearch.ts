// Search and filtering types for Claude Flow

export interface SearchFilters {
  query: string;
  entityType: 'agents' | 'tasks' | 'memories' | 'all';
  status?: string[];
  type?: string[];
  priority?: { min: number; max: number };
  dateRange?: { start: Date; end: Date };
  assignedAgent?: string;
  tags?: string[];
}

export interface SearchResult {
  id: string;
  type: 'agent' | 'task' | 'memory';
  title: string;
  description: string;
  relevanceScore: number;
  highlights: string[];
  metadata: Record<string, any>;
}

export interface FilterOption {
  label: string;
  value: string;
  count?: number;
}

export interface FilterConfig {
  status: FilterOption[];
  type: FilterOption[];
  priority: {
    min: number;
    max: number;
    step: number;
  };
}

export interface SearchState {
  isSearching: boolean;
  results: SearchResult[];
  filters: SearchFilters;
  totalResults: number;
  searchHistory: string[];
  savedFilters: SavedFilter[];
}

export interface SavedFilter {
  id: string;
  name: string;
  filters: SearchFilters;
  createdAt: Date;
}

// Agent-specific filters
export interface AgentFilters {
  status: ('active' | 'idle' | 'terminated' | 'error')[];
  type: ('researcher' | 'implementer' | 'reviewer' | 'security' | 'analyst' | 'tester')[];
  priority: { min: number; max: number };
  createdDate: { start?: Date; end?: Date };
  performanceMetrics?: {
    successRate?: { min: number; max: number };
    avgResponseTime?: { min: number; max: number };
  };
}

// Task-specific filters
export interface TaskFilters {
  status: ('pending' | 'running' | 'completed' | 'failed' | 'cancelled')[];
  type: ('research' | 'implementation' | 'review' | 'analysis' | 'testing' | 'documentation' | 'bug-fix' | 'performance')[];
  priority: { min: number; max: number };
  assignedAgent?: string;
  createdDate: { start?: Date; end?: Date };
  updatedDate: { start?: Date; end?: Date };
  duration?: { min?: number; max?: number };
}

// Memory-specific filters
export interface MemoryFilters {
  contentType: ('conversation' | 'document' | 'code' | 'data')[];
  dateRange: { start?: Date; end?: Date };
  sizeRange?: { min?: number; max?: number };
  associatedAgents?: string[];
  associatedTasks?: string[];
  tags?: string[];
}