import { useState, useCallback, useRef } from 'react';
import { 
  BulkOperation, 
  BulkOperationResult, 
  BulkOperationProgress,
  BulkOperationEngine 
} from '@/lib/bulkOperationEngine';
import { ClaudeFlowIntegration } from '@/lib/claudeFlowIntegration';

interface UseBulkOperationsProps {
  integration: ClaudeFlowIntegration | null;
  onOperationComplete?: (result: BulkOperationResult) => void;
  onOperationError?: (error: Error) => void;
}

export function useBulkOperations({
  integration,
  onOperationComplete,
  onOperationError,
}: UseBulkOperationsProps) {
  const [selectedIds, setSelectedIds] = useState<Set<string>>(new Set());
  const [isExecuting, setIsExecuting] = useState(false);
  const [progress, setProgress] = useState<BulkOperationProgress | null>(null);
  const [result, setResult] = useState<BulkOperationResult | null>(null);
  const [pendingOperation, setPendingOperation] = useState<BulkOperation | null>(null);
  
  const engineRef = useRef<BulkOperationEngine | null>(null);

  // Initialize engine when integration is available
  if (integration && !engineRef.current) {
    engineRef.current = new BulkOperationEngine(integration);
    engineRef.current.setProgressCallback(setProgress);
  }

  const toggleSelection = useCallback((id: string) => {
    setSelectedIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  }, []);

  const selectAll = useCallback((ids: string[]) => {
    setSelectedIds(new Set(ids));
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedIds(new Set());
  }, []);

  const isSelected = useCallback((id: string) => {
    return selectedIds.has(id);
  }, [selectedIds]);

  const prepareBulkOperation = useCallback((
    type: 'agent' | 'task',
    action: string,
    parameters?: Record<string, any>
  ) => {
    if (selectedIds.size === 0) {
      return;
    }

    const operation: BulkOperation = {
      type,
      action,
      targetIds: Array.from(selectedIds),
      parameters,
    };

    try {
      BulkOperationEngine.validateOperation(operation);
      setPendingOperation(operation);
      setResult(null); // Clear any previous result
    } catch (error) {
      if (onOperationError) {
        onOperationError(error as Error);
      }
    }
  }, [selectedIds, onOperationError]);

  const executeBulkOperation = useCallback(async () => {
    if (!pendingOperation || !engineRef.current) {
      return;
    }

    setIsExecuting(true);
    setProgress(null);
    setResult(null);

    try {
      const operationResult = await engineRef.current.executeBulkOperation(pendingOperation);
      setResult(operationResult);
      
      // Clear selection if all operations were successful
      if (operationResult.failed.length === 0) {
        clearSelection();
      } else {
        // Remove successful IDs from selection
        const remainingIds = new Set(selectedIds);
        operationResult.successful.forEach(id => remainingIds.delete(id));
        setSelectedIds(remainingIds);
      }

      if (onOperationComplete) {
        onOperationComplete(operationResult);
      }
    } catch (error) {
      if (onOperationError) {
        onOperationError(error as Error);
      }
      setResult({
        successful: [],
        failed: [{
          id: 'operation',
          error: error instanceof Error ? error.message : 'Unknown error',
        }],
        total: pendingOperation.targetIds.length,
      });
    } finally {
      setIsExecuting(false);
      setProgress(null);
    }
  }, [pendingOperation, selectedIds, clearSelection, onOperationComplete, onOperationError]);

  const cancelOperation = useCallback(() => {
    setPendingOperation(null);
    setResult(null);
  }, []);

  return {
    // Selection management
    selectedIds: Array.from(selectedIds),
    selectedCount: selectedIds.size,
    toggleSelection,
    selectAll,
    clearSelection,
    isSelected,

    // Operation management
    prepareBulkOperation,
    executeBulkOperation,
    cancelOperation,
    pendingOperation,
    isExecuting,
    progress,
    result,
  };
}