import { useState, useCallback, useEffect, useMemo } from 'react';
import { SearchEngine } from '@/lib/searchEngine';
import {
  SearchFilters,
  SearchResult,
  SearchState,
  SavedFilter,
  FilterOption,
} from '@/types/claudeFlowSearch';
import { Agent, Task, Memory } from '@/types/claudeFlow';

const SAVED_FILTERS_KEY = 'claudeFlow.savedFilters';
const SEARCH_HISTORY_KEY = 'claudeFlow.searchHistory';

export function useSearch() {
  const searchEngine = useMemo(() => SearchEngine.getInstance(), []);

  const [state, setState] = useState<SearchState>({
    isSearching: false,
    results: [],
    filters: {
      query: '',
      entityType: 'all',
    },
    totalResults: 0,
    searchHistory: [],
    savedFilters: [],
  });

  // Load saved filters and search history from localStorage
  useEffect(() => {
    const savedFilters = localStorage.getItem(SAVED_FILTERS_KEY);
    const searchHistory = localStorage.getItem(SEARCH_HISTORY_KEY);

    if (savedFilters) {
      try {
        const parsed = JSON.parse(savedFilters);
        setState(prev => ({ ...prev, savedFilters: parsed }));
      } catch (error) {
        console.error('Failed to load saved filters:', error);
      }
    }

    if (searchHistory) {
      try {
        const parsed = JSON.parse(searchHistory);
        setState(prev => ({ ...prev, searchHistory: parsed }));
      } catch (error) {
        console.error('Failed to load search history:', error);
      }
    }
  }, []);

  // Save search history to localStorage
  useEffect(() => {
    localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(state.searchHistory));
  }, [state.searchHistory]);

  // Index entities
  const indexAgent = useCallback((agent: Agent) => {
    searchEngine.indexAgent(agent);
  }, [searchEngine]);

  const indexTask = useCallback((task: Task) => {
    searchEngine.indexTask(task);
  }, [searchEngine]);

  const indexMemory = useCallback((memory: Memory) => {
    searchEngine.indexMemory(memory);
  }, [searchEngine]);

  // Batch index entities
  const indexEntities = useCallback((entities: {
    agents?: Agent[];
    tasks?: Task[];
    memories?: Memory[];
  }) => {
    entities.agents?.forEach(agent => searchEngine.indexAgent(agent));
    entities.tasks?.forEach(task => searchEngine.indexTask(task));
    entities.memories?.forEach(memory => searchEngine.indexMemory(memory));
  }, [searchEngine]);

  // Perform search
  const search = useCallback(async (filters?: Partial<SearchFilters>) => {
    const mergedFilters = { ...state.filters, ...filters };
    
    setState(prev => ({
      ...prev,
      isSearching: true,
      filters: mergedFilters,
    }));

    try {
      const results = await searchEngine.search(mergedFilters);
      const history = searchEngine.getSearchHistory();

      setState(prev => ({
        ...prev,
        isSearching: false,
        results,
        totalResults: results.length,
        searchHistory: history,
      }));

      return results;
    } catch (error) {
      console.error('Search failed:', error);
      setState(prev => ({
        ...prev,
        isSearching: false,
        results: [],
        totalResults: 0,
      }));
      return [];
    }
  }, [state.filters, searchEngine]);

  // Update filters
  const updateFilters = useCallback((filters: Partial<SearchFilters>) => {
    setState(prev => ({
      ...prev,
      filters: { ...prev.filters, ...filters },
    }));
  }, []);

  // Clear filters
  const clearFilters = useCallback(() => {
    setState(prev => ({
      ...prev,
      filters: {
        query: '',
        entityType: 'all',
      },
      results: [],
      totalResults: 0,
    }));
  }, []);

  // Save current filters
  const saveFilter = useCallback((name: string) => {
    const newSavedFilter: SavedFilter = {
      id: `filter-${Date.now()}`,
      name,
      filters: state.filters,
      createdAt: new Date(),
    };

    const updatedSavedFilters = [...state.savedFilters, newSavedFilter];
    setState(prev => ({
      ...prev,
      savedFilters: updatedSavedFilters,
    }));

    localStorage.setItem(SAVED_FILTERS_KEY, JSON.stringify(updatedSavedFilters));
  }, [state.filters, state.savedFilters]);

  // Load saved filter
  const loadSavedFilter = useCallback((filterId: string) => {
    const savedFilter = state.savedFilters.find(f => f.id === filterId);
    if (savedFilter) {
      setState(prev => ({
        ...prev,
        filters: savedFilter.filters,
      }));
      search(savedFilter.filters);
    }
  }, [state.savedFilters, search]);

  // Delete saved filter
  const deleteSavedFilter = useCallback((filterId: string) => {
    const updatedSavedFilters = state.savedFilters.filter(f => f.id !== filterId);
    setState(prev => ({
      ...prev,
      savedFilters: updatedSavedFilters,
    }));
    localStorage.setItem(SAVED_FILTERS_KEY, JSON.stringify(updatedSavedFilters));
  }, [state.savedFilters]);

  // Clear search history
  const clearSearchHistory = useCallback(() => {
    searchEngine.clearSearchHistory();
    setState(prev => ({
      ...prev,
      searchHistory: [],
    }));
    localStorage.removeItem(SEARCH_HISTORY_KEY);
  }, [searchEngine]);

  // Get filter options based on current data
  const getFilterOptions = useCallback((): {
    status: FilterOption[];
    type: FilterOption[];
    agents: FilterOption[];
  } => {
    // This would be calculated based on indexed data
    // For now, returning static options
    return {
      status: [
        { label: 'Active', value: 'active' },
        { label: 'Idle', value: 'idle' },
        { label: 'Completed', value: 'completed' },
        { label: 'Failed', value: 'failed' },
        { label: 'Running', value: 'running' },
        { label: 'Pending', value: 'pending' },
      ],
      type: [
        { label: 'Research', value: 'research' },
        { label: 'Implementation', value: 'implementation' },
        { label: 'Review', value: 'review' },
        { label: 'Analysis', value: 'analysis' },
        { label: 'Testing', value: 'testing' },
        { label: 'Documentation', value: 'documentation' },
      ],
      agents: [], // Would be populated from indexed agents
    };
  }, []);

  return {
    // State
    ...state,

    // Actions
    search,
    updateFilters,
    clearFilters,
    saveFilter,
    loadSavedFilter,
    deleteSavedFilter,
    clearSearchHistory,

    // Indexing
    indexAgent,
    indexTask,
    indexMemory,
    indexEntities,

    // Utilities
    getFilterOptions,
  };
}