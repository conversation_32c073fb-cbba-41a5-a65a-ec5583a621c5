import React, { lazy, ComponentType, Suspense } from 'react';
import { Tab, isChatTab, isAgentTab, isAgentExecutionTab, isClaudeFileTab, isProjectSettingsTab } from '@/types/tabTypes';
import { Loader2 } from 'lucide-react';
import { api, type Project, type Session, type ClaudeMdFile } from '@/lib/api';
import { ProjectListDesigned } from '@/components/ProjectListDesigned';
import { SessionList } from '@/components/SessionList';
import { RunningClaudeSessions } from '@/components/RunningClaudeSessions';
import { Button } from '@/components/ui/button';

// Lazy load heavy components
const ClaudeCodeSession = lazy(() => import('@/components/ClaudeCodeSession').then(m => ({ default: m.ClaudeCodeSession })));
const AgentRunOutputViewer = lazy(() => import('@/components/AgentRunOutputViewer'));
const AgentExecution = lazy(() => import('@/components/AgentExecution').then(m => ({ default: m.AgentExecution })));
const CreateAgent = lazy(() => import('@/components/CreateAgent').then(m => ({ default: m.CreateAgent })));
const MCPManager = lazy(() => import('@/components/MCPManagerEnhanced').then(m => ({ default: m.MCPManagerEnhanced })));
const Settings = lazy(() => import('@/components/SettingsEnhanced').then(m => ({ default: m.SettingsEnhanced })));
const MarkdownEditor = lazy(() => import('@/components/MarkdownEditor').then(m => ({ default: m.MarkdownEditor })));
const CCAgents = lazy(() => import('@/components/CCAgents').then(m => ({ default: m.CCAgents })));
const ProjectSettings = lazy(() => import('@/components/ProjectSettings').then(m => ({ default: m.ProjectSettings })));
const ClaudeFlowManager = lazy(() => import('@/components/ClaudeFlowManager'));

/**
 * Base interface for tab content components
 */
interface TabContentProps {
  tab: Tab;
  onBack?: () => void;
}

/**
 * Interface for tab content handlers
 */
interface TabContentHandler {
  component: ComponentType<any>;
  props: (tab: Tab, handlers: TabContentHandlers) => any;
  requiresData?: string[]; // Required fields from tab
}

/**
 * Handlers for tab interactions
 */
interface TabContentHandlers {
  onBack: () => void;
  updateTab: (id: string, updates: Partial<Tab>) => void;
  createChatTab: () => void;
  closeTab: (id: string) => void;
  createProjectSettingsTab: (projectId: string, projectPath: string, projectData?: any) => string | null;
}

/**
 * Projects view component with state management
 */
const ProjectsView: React.FC<{ tab: Tab; handlers: TabContentHandlers }> = ({ tab, handlers }) => {
  const [projects, setProjects] = React.useState<Project[]>([]);
  const [selectedProject, setSelectedProject] = React.useState<Project | null>(null);
  const [sessions, setSessions] = React.useState<Session[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const loadProjects = async () => {
    try {
      setLoading(true);
      setError(null);
      const projectList = await api.listProjects();
      setProjects(projectList);
    } catch (err) {
      console.error("Failed to load projects:", err);
      setError("Failed to load projects. Please ensure ~/.claude directory exists.");
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    loadProjects();
  }, []);

  const handleProjectClick = async (project: Project) => {
    try {
      setLoading(true);
      setError(null);
      const sessionList = await api.getProjectSessions(project.id);
      setSessions(sessionList);
      setSelectedProject(project);
    } catch (err) {
      console.error("Failed to load sessions:", err);
      setError("Failed to load sessions for this project.");
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    setSelectedProject(null);
    setSessions([]);
  };

  return (
    <div className="h-full overflow-y-auto">
      <div className="container mx-auto p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold tracking-tight">CC Projects</h1>
          <p className="mt-1 text-sm text-muted-foreground">
            Browse your Claude Code sessions
          </p>
        </div>

        {error && (
          <div className="mb-4 rounded-lg border border-destructive/50 bg-destructive/10 p-3 text-xs text-destructive max-w-2xl">
            {error}
          </div>
        )}

        {loading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        )}

        {!loading && (
          <>
            {selectedProject ? (
              <SessionList
                sessions={sessions}
                projectPath={selectedProject.path}
                onBack={handleBack}
                onSessionClick={(session) => {
                  handlers.updateTab(tab.id, {
                    type: 'chat',
                    title: session.project_path.split('/').pop() || 'Session',
                    sessionId: session.id,
                    sessionData: session,
                    initialProjectPath: session.project_path,
                  });
                }}
                onEditClaudeFile={(file: ClaudeMdFile) => {
                  window.dispatchEvent(new CustomEvent('open-claude-file', { 
                    detail: { file } 
                  }));
                }}
              />
            ) : (
              <>
                <div className="mb-4">
                  <Button
                    onClick={handlers.createChatTab}
                    size="default"
                    className="w-full max-w-md"
                  >
                    New Claude Code session
                  </Button>
                </div>

                <RunningClaudeSessions />

                {projects.length > 0 ? (
                  <ProjectListDesigned
                    projects={projects}
                    onProjectClick={handleProjectClick}
                    onProjectSettings={(project) => {
                      handlers.createProjectSettingsTab(project.id, project.path, project);
                    }}
                    loading={loading}
                    className="animate-fade-in"
                  />
                ) : (
                  <div className="py-8 text-center">
                    <p className="text-sm text-muted-foreground">
                      No projects found in ~/.claude/projects
                    </p>
                  </div>
                )}
              </>
            )}
          </>
        )}
      </div>
    </div>
  );
};

/**
 * Registry of tab content handlers
 */
const tabContentRegistry: Record<Tab['type'], TabContentHandler> = {
  projects: {
    component: ProjectsView,
    props: (tab, handlers) => ({ tab, handlers })
  },

  chat: {
    component: ClaudeCodeSession,
    props: (tab, handlers) => {
      if (!isChatTab(tab)) throw new Error('Invalid tab type for chat component');
      return {
        session: tab.sessionData,
        initialProjectPath: tab.initialProjectPath || tab.sessionId,
        onBack: handlers.onBack
      };
    },
    requiresData: ['sessionId']
  },

  agent: {
    component: AgentRunOutputViewer,
    props: (tab, handlers) => {
      if (!isAgentTab(tab)) throw new Error('Invalid tab type for agent component');
      return {
        agentRunId: tab.agentRunId,
        tabId: tab.id
      };
    },
    requiresData: ['agentRunId']
  },

  mcp: {
    component: MCPManager,
    props: (tab, handlers) => ({
      onBack: handlers.onBack
    })
  },

  settings: {
    component: Settings,
    props: (tab, handlers) => ({
      onBack: handlers.onBack
    })
  },

  'claude-md': {
    component: MarkdownEditor,
    props: (tab, handlers) => ({
      onBack: handlers.onBack
    })
  },

  'claude-file': {
    component: () => <div className="p-4">Claude file editor not yet implemented in tabs</div>,
    props: (tab, handlers) => {
      if (!isClaudeFileTab(tab)) throw new Error('Invalid tab type for claude file component');
      return {};
    },
    requiresData: ['claudeFileId']
  },

  'agent-execution': {
    component: AgentExecution,
    props: (tab, handlers) => {
      if (!isAgentExecutionTab(tab)) throw new Error('Invalid tab type for agent execution component');
      return {
        agent: tab.agentData,
        onBack: handlers.onBack
      };
    },
    requiresData: ['agentData']
  },

  'create-agent': {
    component: CreateAgent,
    props: (tab, handlers) => ({
      onAgentCreated: () => handlers.closeTab(tab.id),
      onBack: handlers.onBack
    })
  },

  'import-agent': {
    component: () => <div className="p-4">Import agent functionality coming soon...</div>,
    props: (tab, handlers) => ({})
  },

  'cc-agents': {
    component: CCAgents,
    props: (tab, handlers) => ({
      onBack: handlers.onBack
    })
  },


  'claude-flow': {
    component: ClaudeFlowManager,
    props: (tab, handlers) => ({
      onBack: handlers.onBack
    })
  },

  'project-settings': {
    component: ProjectSettings,
    props: (tab, handlers) => {
      if (!isProjectSettingsTab(tab)) throw new Error('Invalid tab type for project settings component');
      return {
        project: tab.projectData || { id: tab.projectId, path: tab.projectPath },
        onBack: handlers.onBack
      };
    },
    requiresData: ['projectId', 'projectPath']
  }
};

/**
 * Error boundary for tab content
 */
class TabContentErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Tab content error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="p-4 text-center">
          <p className="text-destructive">Something went wrong loading this tab.</p>
          <Button 
            variant="outline" 
            size="sm" 
            className="mt-2"
            onClick={() => this.setState({ hasError: false })}
          >
            Try Again
          </Button>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Tab content factory - creates content for tabs using registered handlers
 */
export class TabContentFactory {
  /**
   * Creates content component for a given tab
   */
  static createContent(tab: Tab, handlers: TabContentHandlers): React.ReactElement {
    const handler = tabContentRegistry[tab.type];
    
    if (!handler) {
      return <div className="p-4">Unknown tab type: {tab.type}</div>;
    }

    // Validate required data
    if (handler.requiresData) {
      const missingData = handler.requiresData.filter(field => !tab[field as keyof Tab]);
      if (missingData.length > 0) {
        return (
          <div className="p-4">
            Missing required data: {missingData.join(', ')}
          </div>
        );
      }
    }

    const Component = handler.component;
    const props = handler.props(tab, handlers);

    return (
      <TabContentErrorBoundary>
        <Suspense
          fallback={
            <div className="flex items-center justify-center h-full">
              <Loader2 className="w-8 h-8 animate-spin text-muted-foreground" />
            </div>
          }
        >
          <Component {...props} />
        </Suspense>
      </TabContentErrorBoundary>
    );
  }

  /**
   * Register a new tab content handler
   */
  static registerHandler(type: string, handler: TabContentHandler) {
    tabContentRegistry[type as Tab['type']] = handler;
  }

  /**
   * Get all registered tab types
   */
  static getRegisteredTypes(): Tab['type'][] {
    return Object.keys(tabContentRegistry) as Tab['type'][];
  }
}

export default TabContentFactory;