import { ClaudeFlowIntegration } from './claudeFlowIntegration';

export interface BulkOperation {
  type: 'agent' | 'task';
  action: string;
  targetIds: string[];
  parameters?: Record<string, any>;
}

export interface BulkOperationResult {
  successful: string[];
  failed: { id: string; error: string }[];
  total: number;
}

export interface BulkOperationProgress {
  current: number;
  total: number;
  currentOperation: string;
  percentage: number;
}

export class BulkOperationEngine {
  private integration: ClaudeFlowIntegration;
  private onProgress?: (progress: BulkOperationProgress) => void;

  constructor(integration: ClaudeFlowIntegration) {
    this.integration = integration;
  }

  setProgressCallback(callback: (progress: BulkOperationProgress) => void) {
    this.onProgress = callback;
  }

  private updateProgress(current: number, total: number, currentOperation: string) {
    if (this.onProgress) {
      this.onProgress({
        current,
        total,
        currentOperation,
        percentage: Math.round((current / total) * 100),
      });
    }
  }

  async executeBulkOperation(operation: BulkOperation): Promise<BulkOperationResult> {
    switch (operation.type) {
      case 'agent':
        return this.executeAgentBulkOperation(operation);
      case 'task':
        return this.executeTaskBulkOperation(operation);
      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
  }

  private async executeAgentBulkOperation(operation: BulkOperation): Promise<BulkOperationResult> {
    const result: BulkOperationResult = {
      successful: [],
      failed: [],
      total: operation.targetIds.length,
    };

    for (let i = 0; i < operation.targetIds.length; i++) {
      const agentId = operation.targetIds[i];
      this.updateProgress(i + 1, operation.targetIds.length, `Processing agent ${agentId}`);

      try {
        switch (operation.action) {
          case 'terminate':
            await this.integration.terminateAgent(
              agentId,
              operation.parameters?.reason || 'Bulk termination'
            );
            result.successful.push(agentId);
            break;

          case 'changePriority':
            if (!operation.parameters?.priority) {
              throw new Error('Priority parameter required');
            }
            // Note: This would require an updateAgent method in the integration
            // For now, we'll mark it as a placeholder
            console.log(`Would update agent ${agentId} priority to ${operation.parameters.priority}`);
            result.successful.push(agentId);
            break;

          case 'export':
            // Placeholder for export functionality
            console.log(`Would export agent ${agentId} configuration`);
            result.successful.push(agentId);
            break;

          case 'duplicate':
            // Placeholder for duplication functionality
            console.log(`Would duplicate agent ${agentId}`);
            result.successful.push(agentId);
            break;

          default:
            throw new Error(`Unknown agent action: ${operation.action}`);
        }
      } catch (error) {
        result.failed.push({
          id: agentId,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return result;
  }

  private async executeTaskBulkOperation(operation: BulkOperation): Promise<BulkOperationResult> {
    const result: BulkOperationResult = {
      successful: [],
      failed: [],
      total: operation.targetIds.length,
    };

    for (let i = 0; i < operation.targetIds.length; i++) {
      const taskId = operation.targetIds[i];
      this.updateProgress(i + 1, operation.targetIds.length, `Processing task ${taskId}`);

      try {
        switch (operation.action) {
          case 'cancel':
            await this.integration.cancelTask(
              taskId,
              operation.parameters?.reason || 'Bulk cancellation'
            );
            result.successful.push(taskId);
            break;

          case 'reassign':
            if (!operation.parameters?.agentId) {
              throw new Error('Agent ID parameter required');
            }
            // Note: This would require a reassignTask method in the integration
            console.log(`Would reassign task ${taskId} to agent ${operation.parameters.agentId}`);
            result.successful.push(taskId);
            break;

          case 'changePriority':
            if (!operation.parameters?.priority) {
              throw new Error('Priority parameter required');
            }
            // Note: This would require an updateTask method in the integration
            console.log(`Would update task ${taskId} priority to ${operation.parameters.priority}`);
            result.successful.push(taskId);
            break;

          case 'delete':
            // Note: This would require a deleteTask method in the integration
            console.log(`Would delete task ${taskId}`);
            result.successful.push(taskId);
            break;

          case 'export':
            // Placeholder for export functionality
            console.log(`Would export task ${taskId} results`);
            result.successful.push(taskId);
            break;

          case 'clone':
            // Placeholder for cloning functionality
            console.log(`Would clone task ${taskId}`);
            result.successful.push(taskId);
            break;

          default:
            throw new Error(`Unknown task action: ${operation.action}`);
        }
      } catch (error) {
        result.failed.push({
          id: taskId,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    return result;
  }

  // Utility methods for validating bulk operations
  static getAvailableAgentActions(): string[] {
    return ['terminate', 'changePriority', 'export', 'duplicate'];
  }

  static getAvailableTaskActions(): string[] {
    return ['cancel', 'reassign', 'changePriority', 'delete', 'export', 'clone'];
  }

  static validateOperation(operation: BulkOperation): void {
    if (!operation.targetIds || operation.targetIds.length === 0) {
      throw new Error('No target IDs provided');
    }

    const availableActions = operation.type === 'agent'
      ? this.getAvailableAgentActions()
      : this.getAvailableTaskActions();

    if (!availableActions.includes(operation.action)) {
      throw new Error(`Invalid action '${operation.action}' for type '${operation.type}'`);
    }

    // Validate required parameters
    switch (operation.action) {
      case 'changePriority':
        if (!operation.parameters?.priority || 
            operation.parameters.priority < 1 || 
            operation.parameters.priority > 10) {
          throw new Error('Priority must be between 1 and 10');
        }
        break;
      case 'reassign':
        if (!operation.parameters?.agentId) {
          throw new Error('Agent ID is required for reassignment');
        }
        break;
    }
  }
}