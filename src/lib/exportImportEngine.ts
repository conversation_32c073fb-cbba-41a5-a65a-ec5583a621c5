import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>emoryEntry } from '@/lib/claudeFlowIntegration';
import { saveAs } from 'file-saver';
import J<PERSON>Z<PERSON> from 'jszip';

export interface ExportOptions {
  includeSettings: boolean;
  includeAgents: boolean;
  includeTasks: boolean;
  includeMemory: boolean;
  includeMetrics: boolean;
  format: 'json' | 'csv' | 'yaml' | 'zip';
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface ImportOptions {
  overwriteExisting: boolean;
  mergeConflicts: 'skip' | 'overwrite' | 'rename';
  validateSchema: boolean;
  createBackup: boolean;
}

export interface ExportData {
  version: string;
  exportDate: string;
  claudeFlow: {
    settings?: Record<string, any>;
    agents?: ClaudeFlowAgent[];
    tasks?: ClaudeFlowTask[];
    memory?: ClaudeFlowMemoryEntry[];
    metrics?: Record<string, any>;
  };
}

export interface ImportResult {
  success: boolean;
  imported: {
    settings: number;
    agents: number;
    tasks: number;
    memory: number;
  };
  errors: Array<{
    type: string;
    message: string;
    item?: any;
  }>;
  conflicts: Array<{
    type: string;
    existing: any;
    incoming: any;
    resolution: 'skipped' | 'overwritten' | 'renamed';
  }>;
}

export class ExportImportEngine {
  private static instance: ExportImportEngine;

  private constructor() {}

  static getInstance(): ExportImportEngine {
    if (!ExportImportEngine.instance) {
      ExportImportEngine.instance = new ExportImportEngine();
    }
    return ExportImportEngine.instance;
  }

  /**
   * Export data based on provided options
   */
  async exportData(
    data: {
      settings?: Record<string, any>;
      agents?: ClaudeFlowAgent[];
      tasks?: ClaudeFlowTask[];
      memory?: ClaudeFlowMemoryEntry[];
      metrics?: Record<string, any>;
    },
    options: ExportOptions
  ): Promise<void> {
    const exportData: ExportData = {
      version: '1.0.0',
      exportDate: new Date().toISOString(),
      claudeFlow: {}
    };

    // Build export data based on options
    if (options.includeSettings && data.settings) {
      exportData.claudeFlow.settings = data.settings;
    }
    if (options.includeAgents && data.agents) {
      exportData.claudeFlow.agents = this.filterByDateRange(data.agents, options.dateRange);
    }
    if (options.includeTasks && data.tasks) {
      exportData.claudeFlow.tasks = this.filterByDateRange(data.tasks, options.dateRange);
    }
    if (options.includeMemory && data.memory) {
      exportData.claudeFlow.memory = this.filterByDateRange(data.memory, options.dateRange);
    }
    if (options.includeMetrics && data.metrics) {
      exportData.claudeFlow.metrics = data.metrics;
    }

    // Export based on format
    switch (options.format) {
      case 'json':
        await this.exportJSON(exportData);
        break;
      case 'csv':
        await this.exportCSV(exportData);
        break;
      case 'yaml':
        await this.exportYAML(exportData);
        break;
      case 'zip':
        await this.exportZIP(exportData);
        break;
    }
  }

  /**
   * Import data with validation and conflict resolution
   */
  async importData(
    file: File,
    options: ImportOptions,
    callbacks: {
      onProgress?: (progress: number) => void;
      onConflict?: (conflict: any) => Promise<'skip' | 'overwrite' | 'rename'>;
    }
  ): Promise<ImportResult> {
    const result: ImportResult = {
      success: false,
      imported: {
        settings: 0,
        agents: 0,
        tasks: 0,
        memory: 0
      },
      errors: [],
      conflicts: []
    };

    try {
      // Parse file based on type
      const data = await this.parseImportFile(file);
      
      // Validate schema if requested
      if (options.validateSchema) {
        const validation = this.validateImportSchema(data);
        if (!validation.valid) {
          result.errors.push(...validation.errors);
          return result;
        }
      }

      // Create backup if requested
      if (options.createBackup) {
        await this.createBackup();
      }

      // Import each data type
      if (data.claudeFlow.settings) {
        const settingsResult = await this.importSettings(
          data.claudeFlow.settings,
          options,
          callbacks
        );
        result.imported.settings = settingsResult.imported;
        result.conflicts.push(...settingsResult.conflicts);
      }

      if (data.claudeFlow.agents) {
        const agentsResult = await this.importAgents(
          data.claudeFlow.agents,
          options,
          callbacks
        );
        result.imported.agents = agentsResult.imported;
        result.conflicts.push(...agentsResult.conflicts);
      }

      if (data.claudeFlow.tasks) {
        const tasksResult = await this.importTasks(
          data.claudeFlow.tasks,
          options,
          callbacks
        );
        result.imported.tasks = tasksResult.imported;
        result.conflicts.push(...tasksResult.conflicts);
      }

      if (data.claudeFlow.memory) {
        const memoryResult = await this.importMemory(
          data.claudeFlow.memory,
          options,
          callbacks
        );
        result.imported.memory = memoryResult.imported;
        result.conflicts.push(...memoryResult.conflicts);
      }

      result.success = true;
    } catch (error) {
      result.errors.push({
        type: 'import_error',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    return result;
  }

  // Private helper methods
  private filterByDateRange<T extends { createdAt?: string; timestamp?: string }>(
    items: T[],
    dateRange?: { start: Date; end: Date }
  ): T[] {
    if (!dateRange) return items;

    return items.filter(item => {
      const date = new Date(item.createdAt || item.timestamp || '');
      return date >= dateRange.start && date <= dateRange.end;
    });
  }

  private async exportJSON(data: ExportData): Promise<void> {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    saveAs(blob, `claude-flow-export-${Date.now()}.json`);
  }

  private async exportCSV(data: ExportData): Promise<void> {
    const csv: string[] = [];
    
    // Export agents as CSV
    if (data.claudeFlow.agents) {
      csv.push('AGENTS');
      csv.push('ID,Name,Type,Status,Priority,Created');
      data.claudeFlow.agents.forEach(agent => {
        csv.push(`${agent.id},${agent.name},${agent.type},${agent.status},${agent.priority},${agent.createdAt}`);
      });
      csv.push('');
    }

    // Export tasks as CSV
    if (data.claudeFlow.tasks) {
      csv.push('TASKS');
      csv.push('ID,Type,Status,Priority,Agent ID,Created');
      data.claudeFlow.tasks.forEach(task => {
        csv.push(`${task.id},${task.type},${task.status},${task.priority},${task.agentId},${task.createdAt}`);
      });
    }

    const blob = new Blob([csv.join('\n')], { type: 'text/csv' });
    saveAs(blob, `claude-flow-export-${Date.now()}.csv`);
  }

  private async exportYAML(data: ExportData): Promise<void> {
    // Simple YAML conversion (you might want to use a proper YAML library)
    const yaml = this.objectToYAML(data);
    const blob = new Blob([yaml], { type: 'text/yaml' });
    saveAs(blob, `claude-flow-export-${Date.now()}.yaml`);
  }

  private async exportZIP(data: ExportData): Promise<void> {
    const zip = new JSZip();
    
    // Add main export file
    zip.file('export.json', JSON.stringify(data, null, 2));
    
    // Add separate files for each data type
    if (data.claudeFlow.agents) {
      zip.file('agents.json', JSON.stringify(data.claudeFlow.agents, null, 2));
    }
    if (data.claudeFlow.tasks) {
      zip.file('tasks.json', JSON.stringify(data.claudeFlow.tasks, null, 2));
    }
    if (data.claudeFlow.memory) {
      zip.file('memory.json', JSON.stringify(data.claudeFlow.memory, null, 2));
    }
    if (data.claudeFlow.settings) {
      zip.file('settings.json', JSON.stringify(data.claudeFlow.settings, null, 2));
    }
    
    // Add metadata
    zip.file('metadata.json', JSON.stringify({
      version: data.version,
      exportDate: data.exportDate,
      itemCounts: {
        agents: data.claudeFlow.agents?.length || 0,
        tasks: data.claudeFlow.tasks?.length || 0,
        memory: data.claudeFlow.memory?.length || 0
      }
    }, null, 2));

    const blob = await zip.generateAsync({ type: 'blob' });
    saveAs(blob, `claude-flow-export-${Date.now()}.zip`);
  }

  private objectToYAML(obj: any, indent = 0): string {
    const spaces = ' '.repeat(indent);
    let yaml = '';

    for (const [key, value] of Object.entries(obj)) {
      if (value === null || value === undefined) {
        yaml += `${spaces}${key}: null\n`;
      } else if (typeof value === 'object' && !Array.isArray(value)) {
        yaml += `${spaces}${key}:\n${this.objectToYAML(value, indent + 2)}`;
      } else if (Array.isArray(value)) {
        yaml += `${spaces}${key}:\n`;
        value.forEach(item => {
          if (typeof item === 'object') {
            yaml += `${spaces}- \n${this.objectToYAML(item, indent + 4)}`;
          } else {
            yaml += `${spaces}- ${item}\n`;
          }
        });
      } else {
        yaml += `${spaces}${key}: ${value}\n`;
      }
    }

    return yaml;
  }

  private async parseImportFile(file: File): Promise<ExportData> {
    const text = await file.text();
    
    if (file.name.endsWith('.json')) {
      return JSON.parse(text);
    } else if (file.name.endsWith('.zip')) {
      // Handle ZIP file parsing
      const zip = new JSZip();
      const contents = await zip.loadAsync(file);
      const exportFile = await contents.file('export.json')?.async('string');
      if (!exportFile) {
        throw new Error('Invalid ZIP file: missing export.json');
      }
      return JSON.parse(exportFile);
    } else {
      throw new Error('Unsupported file format');
    }
  }

  private validateImportSchema(data: any): { valid: boolean; errors: Array<{ type: string; message: string }> } {
    const errors: Array<{ type: string; message: string }> = [];

    // Check required fields
    if (!data.version) {
      errors.push({ type: 'schema_error', message: 'Missing version field' });
    }
    if (!data.claudeFlow) {
      errors.push({ type: 'schema_error', message: 'Missing claudeFlow field' });
    }

    // Validate data structure
    if (data.claudeFlow?.agents && !Array.isArray(data.claudeFlow.agents)) {
      errors.push({ type: 'schema_error', message: 'Agents must be an array' });
    }
    if (data.claudeFlow?.tasks && !Array.isArray(data.claudeFlow.tasks)) {
      errors.push({ type: 'schema_error', message: 'Tasks must be an array' });
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private async createBackup(): Promise<void> {
    // Implementation depends on your storage strategy
    console.log('Creating backup before import...');
  }

  private async importSettings(
    settings: Record<string, any>,
    options: ImportOptions,
    callbacks: any
  ): Promise<{ imported: number; conflicts: any[] }> {
    // Implementation for importing settings
    return { imported: Object.keys(settings).length, conflicts: [] };
  }

  private async importAgents(
    agents: ClaudeFlowAgent[],
    options: ImportOptions,
    callbacks: any
  ): Promise<{ imported: number; conflicts: any[] }> {
    // Implementation for importing agents with conflict resolution
    return { imported: agents.length, conflicts: [] };
  }

  private async importTasks(
    tasks: ClaudeFlowTask[],
    options: ImportOptions,
    callbacks: any
  ): Promise<{ imported: number; conflicts: any[] }> {
    // Implementation for importing tasks with conflict resolution
    return { imported: tasks.length, conflicts: [] };
  }

  private async importMemory(
    memory: ClaudeFlowMemoryEntry[],
    options: ImportOptions,
    callbacks: any
  ): Promise<{ imported: number; conflicts: any[] }> {
    // Implementation for importing memory with conflict resolution
    return { imported: memory.length, conflicts: [] };
  }
}