/**
 * <PERSON>-<PERSON> Integration Layer for <PERSON>
 * Provides seamless integration between <PERSON>'s GUI and <PERSON><PERSON><PERSON>'s agent orchestration system
 */

import { MCPClient } from './mcpClient';

export interface ClaudeFlowConfig {
  /** Claude-Flow installation path */
  installPath?: string;
  /** Server host for claude-flow MCP server */
  host: string;
  /** Server port for claude-flow MCP server */
  port: number;
  /** Whether to auto-start claude-flow service */
  autoStart: boolean;
  /** Working directory for claude-flow */
  workingDirectory?: string;
}

export interface ClaudeFlowAgent {
  id: string;
  name: string;
  type: string;
  status: 'idle' | 'running' | 'paused' | 'error' | 'terminated';
  capabilities: string[];
  systemPrompt?: string;
  taskDescription?: string;
  maxConcurrentTasks: number;
  priority: number;
  createdAt: string;
  lastActivity?: string;
}

export interface ClaudeFlowTask {
  id: string;
  type: string;
  description: string;
  status: 'pending' | 'queued' | 'assigned' | 'running' | 'completed' | 'failed' | 'cancelled';
  priority: number;
  agentId?: string;
  createdAt: string;
  updatedAt: string;
  input?: any;
  output?: any;
  error?: string;
}

export interface ClaudeFlowMemoryEntry {
  id: string;
  agentId: string;
  sessionId: string;
  type: 'observation' | 'insight' | 'decision' | 'artifact' | 'error';
  content: string;
  context?: any;
  tags: string[];
  timestamp: string;
}

export interface ClaudeFlowSystemStatus {
  orchestrator: {
    running: boolean;
    uptime: number;
    totalAgents: number;
    activeAgents: number;
    totalTasks: number;
    completedTasks: number;
  };
  memory: {
    totalEntries: number;
    memoryUsage: number;
  };
  performance: {
    avgResponseTime: number;
    throughput: number;
    errorRate: number;
  };
}

/**
 * Claude-Flow MCP Server Configuration
 * This configuration can be added to Claudia's MCP manager
 */
export const CLAUDE_FLOW_MCP_CONFIG = {
  name: 'claude-flow',
  description: 'Claude-Flow Agent Orchestration System',
  command: 'npx',
  args: ['claude-flow', 'mcp-server'],
  env: {
    CLAUDE_FLOW_PORT: '8765',
    CLAUDE_FLOW_HOST: 'localhost',
    NODE_ENV: 'production'
  },
  capabilities: [
    'agents/spawn',
    'agents/list', 
    'agents/terminate',
    'agents/info',
    'tasks/create',
    'tasks/list',
    'tasks/status',
    'tasks/cancel',
    'tasks/assign',
    'memory/query',
    'memory/store',
    'memory/delete',
    'system/status',
    'system/metrics',
    'system/health',
    'config/get',
    'config/update',
    'workflow/execute',
    'workflow/create',
    'terminal/execute',
    'terminal/list'
  ],
  category: 'agent-orchestration',
  tags: ['agents', 'orchestration', 'swarm', 'tasks', 'memory'],
  documentation: {
    quickStart: 'Claude-Flow provides advanced agent orchestration with swarm capabilities, memory management, and task coordination.',
    examples: [
      {
        title: 'Spawn a Research Agent',
        code: `// Spawn a research agent
const agent = await tools.invoke('agents/spawn', {
  type: 'researcher',
  name: 'Research Assistant',
  capabilities: ['web-search', 'analysis', 'reporting'],
  systemPrompt: 'You are a research assistant specialized in gathering and analyzing information.'
});`
      },
      {
        title: 'Create and Assign Task',
        code: `// Create a task and assign to agent
const task = await tools.invoke('tasks/create', {
  type: 'research',
  description: 'Analyze latest AI developments',
  assignToAgentType: 'researcher',
  priority: 8
});`
      },
      {
        title: 'Query Agent Memory',
        code: `// Query agent memory for insights
const memories = await tools.invoke('memory/query', {
  agentId: 'agent_123',
  type: 'insight',
  search: 'machine learning trends',
  limit: 10
});`
      }
    ]
  }
};

/**
 * Claude-Flow Integration Manager
 * Handles communication with claude-flow services and provides a high-level API
 */
export class ClaudeFlowIntegration {
  private config: ClaudeFlowConfig;
  private mcpClient?: MCPClient;

  constructor(config: ClaudeFlowConfig) {
    this.config = config;
  }

  /**
   * Initialize the integration with MCP client
   */
  async initialize(mcpClient: MCPClient): Promise<void> {
    this.mcpClient = mcpClient;
    await this.mcpClient.initialize();
    
    // Check if claude-flow service is running
    const status = await this.getSystemStatus();
    console.log('Claude-Flow system status:', status);
  }

  /**
   * Spawn a new agent
   */
  async spawnAgent(config: {
    type: string;
    name: string;
    capabilities?: string[];
    systemPrompt?: string;
    maxConcurrentTasks?: number;
    priority?: number;
  }): Promise<ClaudeFlowAgent> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('agents/spawn', {
      type: config.type,
      name: config.name,
      capabilities: config.capabilities || [],
      systemPrompt: config.systemPrompt,
      maxConcurrentTasks: config.maxConcurrentTasks || 3,
      priority: config.priority || 5
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to spawn agent');
    }

    return {
      id: response.result.agentId,
      name: config.name,
      type: config.type,
      status: 'idle',
      capabilities: config.capabilities || [],
      systemPrompt: config.systemPrompt,
      maxConcurrentTasks: config.maxConcurrentTasks || 3,
      priority: config.priority || 5,
      createdAt: response.result.timestamp
    };
  }

  /**
   * List all agents
   */
  async listAgents(filter?: {
    includeTerminated?: boolean;
    filterByType?: string;
  }): Promise<ClaudeFlowAgent[]> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('agents/list', filter || {});
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to list agents');
    }

    return response.result.agents.map((agent: any) => ({
      id: agent.id,
      name: agent.name,
      type: agent.type,
      status: agent.status,
      capabilities: agent.capabilities || [],
      systemPrompt: agent.systemPrompt,
      maxConcurrentTasks: agent.maxConcurrentTasks,
      priority: agent.priority,
      createdAt: agent.createdAt,
      lastActivity: agent.lastActivity
    }));
  }

  /**
   * Terminate an agent
   */
  async terminateAgent(agentId: string, reason?: string): Promise<void> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    await this.mcpClient.invoke('agents/terminate', {
      agentId,
      reason: reason || 'Manual termination from Claudia',
      graceful: true
    });
  }

  /**
   * Create a new task
   */
  async createTask(config: {
    type: string;
    description: string;
    priority?: number;
    assignToAgent?: string;
    assignToAgentType?: string;
    input?: any;
    timeout?: number;
  }): Promise<ClaudeFlowTask> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('tasks/create', {
      type: config.type,
      description: config.description,
      priority: config.priority || 5,
      assignToAgent: config.assignToAgent,
      assignToAgentType: config.assignToAgentType,
      input: config.input,
      timeout: config.timeout
    });

    if (!response.success) {
      throw new Error(response.error || 'Failed to create task');
    }

    return {
      id: response.result.taskId,
      type: config.type,
      description: config.description,
      status: 'pending',
      priority: config.priority || 5,
      agentId: config.assignToAgent,
      createdAt: response.result.timestamp,
      updatedAt: response.result.timestamp,
      input: config.input
    };
  }

  /**
   * List tasks with optional filtering
   */
  async listTasks(filter?: {
    status?: string;
    agentId?: string;
    type?: string;
    limit?: number;
    offset?: number;
  }): Promise<ClaudeFlowTask[]> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('tasks/list', filter || {});
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to list tasks');
    }

    return response.result.tasks.map((task: any) => ({
      id: task.id,
      type: task.type,
      description: task.description,
      status: task.status,
      priority: task.priority,
      agentId: task.agentId,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
      input: task.input,
      output: task.output,
      error: task.error
    }));
  }

  /**
   * Get task status
   */
  async getTaskStatus(taskId: string): Promise<ClaudeFlowTask> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('tasks/status', { taskId });
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to get task status');
    }

    const task = response.result.task;
    
    return {
      id: task.id,
      type: task.type,
      description: task.description,
      status: task.status,
      priority: task.priority,
      agentId: task.agentId,
      createdAt: task.createdAt,
      updatedAt: task.updatedAt,
      input: task.input,
      output: task.output,
      error: task.error
    };
  }

  /**
   * Cancel a task
   */
  async cancelTask(taskId: string, reason?: string): Promise<void> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    await this.mcpClient.invoke('tasks/cancel', {
      taskId,
      reason: reason || 'Manual cancellation from Claudia'
    });
  }

  /**
   * Query agent memory
   */
  async queryMemory(filter: {
    agentId?: string;
    sessionId?: string;
    type?: string;
    tags?: string[];
    search?: string;
    startTime?: string;
    endTime?: string;
    limit?: number;
    offset?: number;
  }): Promise<ClaudeFlowMemoryEntry[]> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('memory/query', filter);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to query memory');
    }

    return response.result.entries.map((entry: any) => ({
      id: entry.id,
      agentId: entry.agentId,
      sessionId: entry.sessionId,
      type: entry.type,
      content: entry.content,
      context: entry.context,
      tags: entry.tags || [],
      timestamp: entry.timestamp
    }));
  }

  /**
   * Store memory entry
   */
  async storeMemory(entry: {
    agentId: string;
    sessionId: string;
    type: 'observation' | 'insight' | 'decision' | 'artifact' | 'error';
    content: string;
    context?: any;
    tags?: string[];
  }): Promise<string> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('memory/store', entry);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to store memory');
    }

    return response.result.entryId;
  }

  /**
   * Get system status
   */
  async getSystemStatus(): Promise<ClaudeFlowSystemStatus> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('system/status', {});
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to get system status');
    }

    const result = response.result;
    
    return {
      orchestrator: {
        running: result.orchestrator?.running || false,
        uptime: result.orchestrator?.uptime || 0,
        totalAgents: result.orchestrator?.totalAgents || 0,
        activeAgents: result.orchestrator?.activeAgents || 0,
        totalTasks: result.orchestrator?.totalTasks || 0,
        completedTasks: result.orchestrator?.completedTasks || 0
      },
      memory: {
        totalEntries: result.memory?.totalEntries || 0,
        memoryUsage: result.memory?.memoryUsage || 0
      },
      performance: {
        avgResponseTime: result.performance?.avgResponseTime || 0,
        throughput: result.performance?.throughput || 0,
        errorRate: result.performance?.errorRate || 0
      }
    };
  }

  /**
   * Test connection to Claude Flow server
   */
  async testConnection(): Promise<{
    success: boolean;
    message: string;
    details?: {
      serverVersion?: string;
      capabilities?: string[];
      status?: ClaudeFlowSystemStatus;
    };
  }> {
    try {
      // First check if MCP client is initialized
      if (!this.mcpClient) {
        return {
          success: false,
          message: 'Claude-Flow integration not initialized'
        };
      }

      // Try to get system health/status
      const healthResponse = await this.mcpClient.invoke('system/health', {});
      
      if (!healthResponse.success) {
        return {
          success: false,
          message: healthResponse.error || 'Health check failed'
        };
      }

      // Get detailed system status
      const status = await this.getSystemStatus();
      
      // Get available capabilities
      const capabilities = await this.mcpClient.listTools();
      
      return {
        success: true,
        message: 'Successfully connected to Claude-Flow',
        details: {
          serverVersion: healthResponse.result?.version || 'Unknown',
          capabilities: capabilities.map(cap => cap.name),
          status
        }
      };
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Connection test failed'
      };
    }
  }

  /**
   * Get system metrics
   */
  async getSystemMetrics(timeRange: '1h' | '6h' | '24h' | '7d' = '1h'): Promise<any> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('system/metrics', { timeRange });
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to get system metrics');
    }

    return response.result.metrics;
  }

  /**
   * Start metrics collection
   */
  async startMetricsCollection(interval: number = 5000): Promise<void> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('metrics/start-collection', { interval });
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to start metrics collection');
    }
  }

  /**
   * Stop metrics collection
   */
  async stopMetricsCollection(): Promise<void> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('metrics/stop-collection', {});
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to stop metrics collection');
    }
  }

  /**
   * Execute terminal command
   */
  async executeCommand(config: {
    command: string;
    args?: string[];
    cwd?: string;
    env?: Record<string, string>;
    timeout?: number;
    terminalId?: string;
  }): Promise<any> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    return await this.mcpClient.invoke('terminal/execute', config);
  }

  /**
   * List terminal sessions
   */
  async listTerminals(): Promise<any[]> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('terminal/list', {});
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to list terminals');
    }

    return response.result.terminals;
  }

  /**
   * Execute a workflow
   */
  async executeWorkflow(config: {
    filePath?: string;
    workflow?: any;
    parameters?: any;
  }): Promise<any> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    return await this.mcpClient.invoke('workflow/execute', config);
  }

  /**
   * Save configuration to MCP server
   */
  async saveConfiguration(config: any): Promise<void> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('config/save', {
      config,
      timestamp: new Date().toISOString()
    });
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to save configuration');
    }
  }

  /**
   * Load configuration from MCP server
   */
  async loadConfiguration(): Promise<any> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('config/load', {});
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to load configuration');
    }

    return response.result.config;
  }

  /**
   * Export configuration
   */
  async exportConfiguration(): Promise<{
    config: any;
    metadata: {
      version: string;
      exportedAt: string;
      serverInfo?: any;
    };
  }> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('config/export', {});
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to export configuration');
    }

    return response.result;
  }

  /**
   * Import configuration
   */
  async importConfiguration(data: {
    config: any;
    metadata?: any;
  }): Promise<void> {
    if (!this.mcpClient) {
      throw new Error('Claude-Flow integration not initialized');
    }

    const response = await this.mcpClient.invoke('config/import', data);
    
    if (!response.success) {
      throw new Error(response.error || 'Failed to import configuration');
    }
  }
}

/**
 * Default Claude-Flow configuration
 */
export const DEFAULT_CLAUDE_FLOW_CONFIG: ClaudeFlowConfig = {
  host: 'localhost',
  port: 8765,
  autoStart: true,
  workingDirectory: typeof process !== 'undefined' && process.cwd ? process.cwd() : '.'
};