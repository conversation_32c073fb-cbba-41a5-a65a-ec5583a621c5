import { Agent, Task, Memory } from '@/types/claudeFlow';
import {
  SearchFilters,
  SearchResult
} from '@/types/claudeFlowSearch';

interface SearchIndexEntry {
  id: string;
  type: 'agent' | 'task' | 'memory';
  title: string;
  description: string;
  content: string;
  metadata: Record<string, any>;
}

export class SearchEngine {
  private static instance: SearchEngine;
  private searchIndex: Map<string, SearchIndexEntry> = new Map();
  private searchHistory: string[] = [];
  private maxHistorySize = 50;

  private constructor() {}

  static getInstance(): SearchEngine {
    if (!SearchEngine.instance) {
      SearchEngine.instance = new SearchEngine();
    }
    return SearchEngine.instance;
  }

  // Index management
  indexAgent(agent: Agent): void {
    const entry: SearchIndexEntry = {
      id: agent.id,
      type: 'agent',
      title: agent.name,
      description: agent.taskDescription || '',
      content: this.concatenateAgentContent(agent),
      metadata: {
        status: agent.status,
        type: agent.type,
        priority: agent.priority,
        createdAt: agent.createdAt,
      },
    };
    this.searchIndex.set(`agent-${agent.id}`, entry);
  }

  indexTask(task: Task): void {
    const entry: SearchIndexEntry = {
      id: task.id,
      type: 'task',
      title: task.description,
      description: task.description || '',
      content: this.concatenateTaskContent(task),
      metadata: {
        status: task.status,
        type: task.type,
        priority: task.priority,
        agentId: task.agentId,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
      },
    };
    this.searchIndex.set(`task-${task.id}`, entry);
  }

  indexMemory(memory: Memory): void {
    const entry: SearchIndexEntry = {
      id: memory.id,
      type: 'memory',
      title: `Memory ${memory.type} - ${memory.agentId}`,
      description: memory.content.substring(0, 200),
      content: memory.content,
      metadata: {
        type: memory.type,
        agentId: memory.agentId,
        sessionId: memory.sessionId,
        timestamp: memory.timestamp,
        size: memory.content.length,
        tags: memory.tags || [],
        context: memory.context,
      },
    };
    this.searchIndex.set(`memory-${memory.id}`, entry);
  }

  // Main search function
  async search(filters: SearchFilters): Promise<SearchResult[]> {
    const results: SearchResult[] = [];
    const query = filters.query.toLowerCase().trim();

    // Add to search history
    if (query && !this.searchHistory.includes(query)) {
      this.searchHistory.unshift(query);
      if (this.searchHistory.length > this.maxHistorySize) {
        this.searchHistory.pop();
      }
    }

    // Filter entries based on entity type
    const entriesToSearch = this.getEntriesByType(filters.entityType);

    // Search and score each entry
    for (const [, entry] of entriesToSearch) {
      const score = this.calculateRelevanceScore(entry, query);
      
      if (score > 0 || !query) {
        // Apply type-specific filters
        if (this.passesFilters(entry, filters)) {
          const highlights = query ? this.extractHighlights(entry, query) : [];
          
          results.push({
            id: entry.id,
            type: entry.type,
            title: entry.title,
            description: entry.description,
            relevanceScore: score,
            highlights,
            metadata: entry.metadata,
          });
        }
      }
    }

    // Sort by relevance score
    return results.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  // Get search history
  getSearchHistory(): string[] {
    return [...this.searchHistory];
  }

  // Clear search history
  clearSearchHistory(): void {
    this.searchHistory = [];
  }

  // Helper methods
  private concatenateAgentContent(agent: Agent): string {
    return [
      agent.name,
      agent.systemPrompt,
      agent.type,
      agent.capabilities?.join(' '),
      agent.status,
    ]
      .filter(Boolean)
      .join(' ')
      .toLowerCase();
  }

  private concatenateTaskContent(task: Task): string {
    return [
      task.description,
      task.type,
      task.status,
      task.output ? JSON.stringify(task.output) : '',
      task.error,
    ]
      .filter(Boolean)
      .join(' ')
      .toLowerCase();
  }

  private getEntriesByType(entityType: string): Map<string, SearchIndexEntry> {
    if (entityType === 'all') {
      return this.searchIndex;
    }

    const filtered = new Map<string, SearchIndexEntry>();
    for (const [key, entry] of this.searchIndex) {
      if (entry.type === entityType.slice(0, -1)) {
        // Remove 's' from plural
        filtered.set(key, entry);
      }
    }
    return filtered;
  }

  private calculateRelevanceScore(entry: SearchIndexEntry, query: string): number {
    if (!query) return 1;

    let score = 0;
    const queryTerms = query.split(/\s+/);

    for (const term of queryTerms) {
      // Title match (highest weight)
      if (entry.title.toLowerCase().includes(term)) {
        score += 10;
      }

      // Description match (medium weight)
      if (entry.description.toLowerCase().includes(term)) {
        score += 5;
      }

      // Content match (lower weight)
      if (entry.content.includes(term)) {
        score += 2;
      }

      // Metadata match (lowest weight)
      const metadataStr = JSON.stringify(entry.metadata).toLowerCase();
      if (metadataStr.includes(term)) {
        score += 1;
      }
    }

    return score;
  }

  private passesFilters(entry: SearchIndexEntry, filters: SearchFilters): boolean {
    // Status filter
    if (filters.status?.length && !filters.status.includes(entry.metadata.status)) {
      return false;
    }

    // Type filter
    if (filters.type?.length && !filters.type.includes(entry.metadata.type)) {
      return false;
    }

    // Priority filter
    if (filters.priority) {
      const priority = entry.metadata.priority || 5;
      if (priority < filters.priority.min || priority > filters.priority.max) {
        return false;
      }
    }

    // Date range filter
    if (filters.dateRange) {
      const createdAt = new Date(entry.metadata.createdAt);
      if (filters.dateRange.start && createdAt < filters.dateRange.start) {
        return false;
      }
      if (filters.dateRange.end && createdAt > filters.dateRange.end) {
        return false;
      }
    }

    // Assigned agent filter (for tasks)
    if (filters.assignedAgent && entry.type === 'task') {
      if (entry.metadata.agentId !== filters.assignedAgent) {
        return false;
      }
    }

    // Tags filter
    if (filters.tags?.length) {
      const entryTags = entry.metadata.tags || [];
      const hasMatchingTag = filters.tags.some(tag => entryTags.includes(tag));
      if (!hasMatchingTag) {
        return false;
      }
    }

    return true;
  }

  private extractHighlights(entry: SearchIndexEntry, query: string): string[] {
    const highlights: string[] = [];
    const queryTerms = query.split(/\s+/);
    const contextLength = 50;

    const searchableText = `${entry.title} ${entry.description} ${entry.content}`;

    for (const term of queryTerms) {
      const regex = new RegExp(`(.{0,${contextLength}})(${term})(.{0,${contextLength}})`, 'gi');
      const matches = searchableText.matchAll(regex);

      for (const match of matches) {
        highlights.push(`...${match[1]}**${match[2]}**${match[3]}...`);
        if (highlights.length >= 3) break;
      }
    }

    return highlights.slice(0, 3);
  }
}

interface SearchIndexEntry {
  id: string;
  type: 'agent' | 'task' | 'memory';
  title: string;
  description: string;
  content: string;
  metadata: Record<string, any>;
}