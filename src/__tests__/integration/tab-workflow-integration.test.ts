/**
 * Integration tests for the tab workflow system
 * Tests the TabContentFactory, event bus, and tab state management
 */

import { TabContentFactory } from '@/lib/tabContentFactory';
import { EventBus } from '@/lib/eventBus';
import type { Tab, ChatTab, ProjectTab, SettingsTab, CrushTab } from '@/types/tabTypes';
import { isChatTab, isProjectTab, isSettingsTab } from '@/types/tabTypes';

// Mock components for testing
const MockProjectsView = () => 'ProjectsView';
const MockClaudeCodeSession = ({ session }: { session?: any }) => `ClaudeCodeSession:${session?.id || 'default'}`;
const MockSettings = () => 'Settings';
const MockCrushSession = ({ sessionId }: { sessionId?: string }) => `CrushSession:${sessionId || 'default'}`;

// Mock the TabContentFactory registry for testing
jest.mock('@/lib/tabContentFactory', () => {
  const originalModule = jest.requireActual('@/lib/tabContentFactory');
  return {
    ...originalModule,
    TabContentFactory: {
      createContent: jest.fn(),
      registerTabType: jest.fn(),
      getRegisteredTypes: jest.fn(() => ['projects', 'chat', 'settings', 'crush']),
    },
  };
});

describe('Tab Workflow Integration', () => {
  let eventBus: EventBus;
  let mockCreateContent: jest.MockedFunction<typeof TabContentFactory.createContent>;

  beforeEach(() => {
    eventBus = new EventBus();
    mockCreateContent = TabContentFactory.createContent as jest.MockedFunction<typeof TabContentFactory.createContent>;
    jest.clearAllMocks();
  });

  afterEach(() => {
    eventBus.removeAllListeners();
  });

  describe('Tab Type Guards', () => {
    it('should correctly identify chat tabs', () => {
      const chatTab: ChatTab = {
        id: 'chat-1',
        type: 'chat',
        title: 'Test Chat',
        status: 'active',
        sessionId: 'session-123',
        sessionData: { messages: [] },
      };

      expect(isChatTab(chatTab)).toBe(true);
      expect(isProjectTab(chatTab)).toBe(false);
      expect(isSettingsTab(chatTab)).toBe(false);
    });

    it('should correctly identify project tabs', () => {
      const projectTab: ProjectTab = {
        id: 'projects-1',
        type: 'projects',
        title: 'Projects',
        status: 'active',
      };

      expect(isProjectTab(projectTab)).toBe(true);
      expect(isChatTab(projectTab)).toBe(false);
      expect(isSettingsTab(projectTab)).toBe(false);
    });

    it('should correctly identify settings tabs', () => {
      const settingsTab: SettingsTab = {
        id: 'settings-1',
        type: 'settings',
        title: 'Settings',
        status: 'active',
      };

      expect(isSettingsTab(settingsTab)).toBe(true);
      expect(isChatTab(settingsTab)).toBe(false);
      expect(isProjectTab(settingsTab)).toBe(false);
    });

  });

  describe('TabContentFactory Integration', () => {
    it('should create content for projects tab', () => {
      const projectTab: ProjectTab = {
        id: 'projects-1',
        type: 'projects',
        title: 'Projects',
        status: 'active',
      };

      const handlers = {
        onBack: jest.fn(),
        onClose: jest.fn(),
        onNavigate: jest.fn(),
      };

      mockCreateContent.mockReturnValue({
        component: MockProjectsView,
        props: { tab: projectTab, handlers },
      });

      const result = TabContentFactory.createContent(projectTab, handlers);

      expect(mockCreateContent).toHaveBeenCalledWith(projectTab, handlers);
      expect(result.component).toBe(MockProjectsView);
      expect(result.props).toEqual({ tab: projectTab, handlers });
    });

    it('should create content for chat tab with session data', () => {
      const chatTab: ChatTab = {
        id: 'chat-1',
        type: 'chat',
        title: 'Test Chat',
        status: 'active',
        sessionId: 'session-123',
        sessionData: { id: 'session-123', messages: [] },
        initialProjectPath: '/test/project',
      };

      const handlers = {
        onBack: jest.fn(),
        onClose: jest.fn(),
        onNavigate: jest.fn(),
      };

      mockCreateContent.mockReturnValue({
        component: MockClaudeCodeSession,
        props: {
          session: chatTab.sessionData,
          initialProjectPath: chatTab.initialProjectPath,
          onBack: handlers.onBack,
        },
      });

      const result = TabContentFactory.createContent(chatTab, handlers);

      expect(mockCreateContent).toHaveBeenCalledWith(chatTab, handlers);
      expect(result.component).toBe(MockClaudeCodeSession);
      expect(result.props.session).toEqual(chatTab.sessionData);
      expect(result.props.initialProjectPath).toBe('/test/project');
      expect(result.props.onBack).toBe(handlers.onBack);
    });

    it('should handle crush tab creation', () => {
      const crushTab: CrushTab = {
        id: 'crush-1',
        type: 'crush',
        title: 'Crush Session',
        status: 'active',
        sessionId: 'crush-session-123',
        projectPath: '/path/to/project',
      };

      const handlers = {
        onBack: jest.fn(),
        onClose: jest.fn(),
        onNavigate: jest.fn(),
      };

      mockCreateContent.mockReturnValue({
        component: MockCrushSession,
        props: {
          sessionId: crushTab.sessionId,
          projectPath: crushTab.projectPath,
          onBack: handlers.onBack,
        },
      });

      const result = TabContentFactory.createContent(crushTab, handlers);

      expect(mockCreateContent).toHaveBeenCalledWith(crushTab, handlers);
      expect(result.component).toBe(MockCrushSession);
      expect(result.props.sessionId).toBe('crush-session-123');
      expect(result.props.projectPath).toBe('/path/to/project');
    });
  });

  describe('Event Bus Integration', () => {
    it('should emit and handle tab creation events', () => {
      const handler = jest.fn();
      
      eventBus.on('tab:create-chat', handler);
      
      const eventData = {
        sessionId: 'session-123',
        title: 'New Chat',
        projectPath: '/project/path',
      };

      eventBus.emit('tab:create-chat', eventData);

      expect(handler).toHaveBeenCalledTimes(1);
      expect(handler).toHaveBeenCalledWith(eventData);
    });

    it('should handle tab close events', () => {
      const handler = jest.fn();
      
      eventBus.on('tab:close', handler);
      
      const eventData = { tabId: 'tab-123' };
      eventBus.emit('tab:close', eventData);

      expect(handler).toHaveBeenCalledWith(eventData);
    });

    it('should handle session opening events', () => {
      const handler = jest.fn();
      
      eventBus.on('tab:open-session', handler);
      
      const eventData = {
        session: {
          id: 'session-123',
          title: 'Test Session',
          messages: [],
        },
      };

      eventBus.emit('tab:open-session', eventData);

      expect(handler).toHaveBeenCalledWith(eventData);
    });

    it('should handle keyboard shortcuts', () => {
      const createChatHandler = jest.fn();
      const closeChatHandler = jest.fn();
      
      eventBus.on('keyboard:create-chat-tab', createChatHandler);
      eventBus.on('keyboard:close-current-tab', closeChatHandler);
      
      eventBus.emit('keyboard:create-chat-tab', undefined);
      eventBus.emit('keyboard:close-current-tab', undefined);

      expect(createChatHandler).toHaveBeenCalledWith(undefined);
      expect(closeChatHandler).toHaveBeenCalledWith(undefined);
    });

    it('should support multiple listeners for the same event', () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      
      eventBus.on('tab:create-chat', handler1);
      eventBus.on('tab:create-chat', handler2);
      
      const eventData = { sessionId: 'test' };
      eventBus.emit('tab:create-chat', eventData);

      expect(handler1).toHaveBeenCalledWith(eventData);
      expect(handler2).toHaveBeenCalledWith(eventData);
    });

    it('should remove listeners correctly', () => {
      const handler = jest.fn();
      
      eventBus.on('tab:close', handler);
      eventBus.off('tab:close', handler);
      
      eventBus.emit('tab:close', { tabId: 'test' });

      expect(handler).not.toHaveBeenCalled();
    });

    it('should handle event bus cleanup', () => {
      const handler1 = jest.fn();
      const handler2 = jest.fn();
      
      eventBus.on('tab:create-chat', handler1);
      eventBus.on('tab:close', handler2);
      
      eventBus.removeAllListeners();
      
      eventBus.emit('tab:create-chat', { sessionId: 'test' });
      eventBus.emit('tab:close', { tabId: 'test' });

      expect(handler1).not.toHaveBeenCalled();
      expect(handler2).not.toHaveBeenCalled();
    });
  });

  describe('Tab Workflow State Management', () => {
    it('should maintain tab state consistency during operations', () => {
      let currentTabs: Tab[] = [];
      
      // Simulate tab creation
      eventBus.on('tab:create-chat', ({ sessionId, title }) => {
        const newTab: ChatTab = {
          id: `chat-${Date.now()}`,
          type: 'chat',
          title: title || 'New Chat',
          status: 'active',
          sessionId,
        };
        currentTabs.push(newTab);
      });

      // Simulate tab closing
      eventBus.on('tab:close', ({ tabId }) => {
        currentTabs = currentTabs.filter(tab => tab.id !== tabId);
      });

      // Create multiple tabs
      eventBus.emit('tab:create-chat', { sessionId: 'session-1', title: 'Chat 1' });
      eventBus.emit('tab:create-chat', { sessionId: 'session-2', title: 'Chat 2' });
      
      expect(currentTabs).toHaveLength(2);
      expect(currentTabs[0].title).toBe('Chat 1');
      expect(currentTabs[1].title).toBe('Chat 2');

      // Close one tab
      eventBus.emit('tab:close', { tabId: currentTabs[0].id });
      
      expect(currentTabs).toHaveLength(1);
      expect(currentTabs[0].title).toBe('Chat 2');
    });

    it('should handle tab status transitions', () => {
      const tabs: Map<string, Tab> = new Map();
      
      // Simulate tab status management
      eventBus.on('tab:status-change', ({ tabId, status }) => {
        const tab = tabs.get(tabId);
        if (tab) {
          tab.status = status;
        }
      });

      // Create initial tab
      const chatTab: ChatTab = {
        id: 'chat-1',
        type: 'chat',
        title: 'Test Chat',
        status: 'idle',
        sessionId: 'session-123',
      };
      
      tabs.set(chatTab.id, chatTab);
      
      // Test status transitions
      eventBus.emit('tab:status-change', { tabId: 'chat-1', status: 'running' });
      expect(tabs.get('chat-1')?.status).toBe('running');
      
      eventBus.emit('tab:status-change', { tabId: 'chat-1', status: 'complete' });
      expect(tabs.get('chat-1')?.status).toBe('complete');
      
      eventBus.emit('tab:status-change', { tabId: 'chat-1', status: 'error' });
      expect(tabs.get('chat-1')?.status).toBe('error');
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid tab types gracefully', () => {
      const invalidTab = {
        id: 'invalid-1',
        type: 'invalid-type',
        title: 'Invalid Tab',
        status: 'active',
      } as any;

      expect(() => {
        isChatTab(invalidTab);
        isProjectTab(invalidTab);
        isSettingsTab(invalidTab);
      }).not.toThrow();

      expect(isChatTab(invalidTab)).toBe(false);
      expect(isProjectTab(invalidTab)).toBe(false);
      expect(isSettingsTab(invalidTab)).toBe(false);
    });

    it('should handle missing session data in chat tabs', () => {
      const chatTabWithoutSession: ChatTab = {
        id: 'chat-1',
        type: 'chat',
        title: 'Chat without session',
        status: 'active',
        sessionId: undefined,
        sessionData: undefined,
      };

      const handlers = {
        onBack: jest.fn(),
        onClose: jest.fn(),
        onNavigate: jest.fn(),
      };

      mockCreateContent.mockReturnValue({
        component: MockClaudeCodeSession,
        props: {
          session: undefined,
          initialProjectPath: undefined,
          onBack: handlers.onBack,
        },
      });

      expect(() => {
        TabContentFactory.createContent(chatTabWithoutSession, handlers);
      }).not.toThrow();

      expect(mockCreateContent).toHaveBeenCalledWith(chatTabWithoutSession, handlers);
    });

    it('should handle event emission with no listeners', () => {
      expect(() => {
        eventBus.emit('tab:create-chat', { sessionId: 'test' });
        eventBus.emit('tab:close', { tabId: 'test' });
        eventBus.emit('nonexistent-event' as any, {});
      }).not.toThrow();
    });

    it('should handle rapid event emissions', () => {
      const handler = jest.fn();
      eventBus.on('tab:create-chat', handler);

      // Emit multiple events rapidly
      for (let i = 0; i < 100; i++) {
        eventBus.emit('tab:create-chat', { sessionId: `session-${i}` });
      }

      expect(handler).toHaveBeenCalledTimes(100);
    });
  });

  describe('Performance and Memory Management', () => {
    it('should handle large numbers of tabs efficiently', () => {
      const tabs: Tab[] = [];
      let eventCount = 0;

      eventBus.on('tab:create-chat', () => {
        eventCount++;
      });

      // Create 1000 tabs to test performance
      const startTime = Date.now();
      
      for (let i = 0; i < 1000; i++) {
        const tab: ChatTab = {
          id: `chat-${i}`,
          type: 'chat',
          title: `Chat ${i}`,
          status: 'active',
          sessionId: `session-${i}`,
        };
        tabs.push(tab);
        eventBus.emit('tab:create-chat', { sessionId: `session-${i}` });
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(tabs).toHaveLength(1000);
      expect(eventCount).toBe(1000);
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should prevent memory leaks with proper listener cleanup', () => {
      const handlers: Array<() => void> = [];

      // Create many listeners
      for (let i = 0; i < 100; i++) {
        const handler = jest.fn();
        handlers.push(handler);
        eventBus.on('tab:create-chat', handler);
      }

      // Remove all listeners
      eventBus.removeAllListeners();

      // Emit event - no handlers should be called
      eventBus.emit('tab:create-chat', { sessionId: 'test' });

      handlers.forEach(handler => {
        expect(handler).not.toHaveBeenCalled();
      });
    });
  });
});