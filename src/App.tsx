import { useState, useEffect } from "react";
import { OutputCacheProvider } from "@/lib/outputCache";
import { Tab<PERSON>rovider } from "@/contexts/TabContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { OptimizedMCPProvider } from "@/contexts/OptimizedMCPContext";
import { TopbarModern } from "@/components/TopbarModern";
import { NFOCredits } from "@/components/NFOCredits";
import { ClaudeBinaryDialog } from "@/components/ClaudeBinaryDialog";
import { Toast, ToastContainer } from "@/components/ui/toast";
import { TabManager } from "@/components/TabManager";
import { TabContent } from "@/components/TabContent";
import { AgentsModal } from "@/components/AgentsModal";
import { useTabState } from "@/hooks/useTabState";
import { useEventBus } from "@/lib/eventBus.tsx";

/**
 * AppContent component - Contains the main app logic, wrapped by providers
 */
function AppContent() {
  const { createClaudeMdTab, createSettingsTab, createMCPTab, createProjectsTab, createClaudeFlowTab } = useTabState();
  const { emit, subscribe } = useEventBus();
  const [showNFO, setShowNFO] = useState(false);
  const [showClaudeBinaryDialog, setShowClaudeBinaryDialog] = useState(false);
  const [toast, setToast] = useState<{ message: string; type: "success" | "error" | "info" } | null>(null);
  const [showAgentsModal, setShowAgentsModal] = useState(false);

  // Keyboard shortcuts for tab navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
      const modKey = isMac ? e.metaKey : e.ctrlKey;
      
      if (modKey) {
        switch (e.key) {
          case 't':
            e.preventDefault();
            emit('keyboard:create-chat-tab', undefined);
            break;
          case 'w':
            e.preventDefault();
            emit('keyboard:close-current-tab', undefined);
            break;
          case 'Tab':
            e.preventDefault();
            if (e.shiftKey) {
              emit('keyboard:switch-to-previous-tab', undefined);
            } else {
              emit('keyboard:switch-to-next-tab', undefined);
            }
            break;
          default:
            // Handle number keys 1-9
            if (e.key >= '1' && e.key <= '9') {
              e.preventDefault();
              const index = parseInt(e.key) - 1;
              emit('keyboard:switch-to-tab-by-index', { index });
            }
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Listen for Claude not found events using event bus
  useEffect(() => {
    const subscription = subscribe('app:claude-not-found', () => {
      setShowClaudeBinaryDialog(true);
    });

    return subscription.unsubscribe;
  }, [subscribe]);

  return (
    <div className="h-screen bg-background flex flex-col">
      {/* Background gradients for visual interest */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-orange-500/5 rounded-full blur-3xl" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl" />
      </div>
      
      {/* Topbar - Always accessible with proper z-index */}
      <div className="relative z-50 border-b border-border/50 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <TopbarModern
          onProjectsClick={() => createProjectsTab()}
          onClaudeClick={() => createClaudeMdTab()}
          onSettingsClick={() => createSettingsTab()}
          onMCPClick={() => createMCPTab()}
          onInfoClick={() => setShowNFO(true)}
          onAgentsClick={() => setShowAgentsModal(true)}
          onClaudeFlowClick={() => createClaudeFlowTab()}
        />
      </div>
      
      {/* Main Content */}
      <div className="flex-1 overflow-hidden relative z-10">
        <div className="h-full flex flex-col">
          <TabManager className="flex-shrink-0 border-b border-border/30" />
          <div className="flex-1 overflow-hidden bg-background/50">
            <TabContent />
          </div>
        </div>
      </div>
      
      {/* NFO Credits Modal */}
      {showNFO && <NFOCredits onClose={() => setShowNFO(false)} />}
      
      {/* Agents Modal */}
      <AgentsModal 
        open={showAgentsModal} 
        onOpenChange={setShowAgentsModal} 
      />
      
      {/* Claude Binary Dialog */}
      <ClaudeBinaryDialog
        open={showClaudeBinaryDialog}
        onOpenChange={setShowClaudeBinaryDialog}
        onSuccess={() => {
          setToast({ message: "Claude binary path saved successfully", type: "success" });
          // Trigger a refresh of the Claude version check
          window.location.reload();
        }}
        onError={(message) => setToast({ message, type: "error" })}
      />
      
      {/* Toast Container */}
      <ToastContainer>
        {toast && (
          <Toast
            message={toast.message}
            type={toast.type}
            onDismiss={() => setToast(null)}
          />
        )}
      </ToastContainer>
      
      <div className="absolute bottom-4 right-4 z-50">
      </div>
    </div>
  );
}

/**
 * Main App component - Wraps the app with providers
 */
function App() {
  return (
    <ThemeProvider>
      <OutputCacheProvider>
        <OptimizedMCPProvider>
          <TabProvider>
            <AppContent />
          </TabProvider>
        </OptimizedMCPProvider>
      </OutputCacheProvider>
    </ThemeProvider>
  );
}

export default App;
