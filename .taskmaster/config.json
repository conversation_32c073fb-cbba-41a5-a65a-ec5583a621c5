{"models": {"main": {"provider": "claude-code", "modelId": "sonnet", "maxTokens": 64000, "temperature": 0.2}, "research": {"provider": "claude-code", "modelId": "opus", "maxTokens": 32000, "temperature": 0.1}, "fallback": {"provider": "claude-code", "modelId": "sonnet", "maxTokens": 64000, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultNumTasks": 10, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "responseLanguage": "English", "defaultTag": "master", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********"}, "claudeCode": {"maxTurns": 10, "appendSystemPrompt": "Focus on creating clear, actionable tasks with proper dependencies based on the PRD phases and requirements"}, "commandSpecific": {"parse-prd": {"maxTurns": 15, "customSystemPrompt": "You are a task breakdown specialist who creates comprehensive, well-structured tasks from PRD documents. Focus on creating 12-15 high-level tasks that cover all 5 phases of the project, with clear titles, descriptions, and logical dependencies."}}}