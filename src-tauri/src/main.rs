// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod cache_storage;
mod checkpoint;
mod claude_binary;
mod commands;
mod process;
mod plan_manager;
mod quality_validator;
mod mode_history;
mod mode_preferences;
mod search;

use checkpoint::state::CheckpointState;
use commands::search::{
    start_session_indexing,
    get_session_indexing_status,
    pause_session_indexing,
};
use search::db::SearchDb;
use commands::agents::{
    cleanup_finished_processes, create_agent, delete_agent, execute_agent, export_agent,
    export_agent_to_file, fetch_github_agent_content, fetch_github_agents, get_agent,
    get_agent_run, get_agent_run_with_real_time_metrics, get_claude_binary_path,
    get_live_session_output, get_session_output, get_session_status, import_agent,
    import_agent_from_file, import_agent_from_github, init_database, kill_agent_session,
    list_agent_runs, list_agent_runs_with_metrics, list_agents, list_claude_installations,
    list_running_sessions, load_agent_session_history, set_claude_binary_path, stream_session_output, update_agent, AgentDb,
};
use commands::claude::{
    cancel_claude_execution, check_auto_checkpoint, check_claude_version, cleanup_old_checkpoints,
    clear_checkpoint_manager, continue_claude_code, create_checkpoint, execute_claude_code,
    find_claude_md_files, fork_from_checkpoint, get_checkpoint_diff, get_checkpoint_settings,
    get_checkpoint_state_stats, get_claude_session_output, get_claude_settings, get_project_sessions,
    get_recently_modified_files, get_session_timeline, get_system_prompt, list_checkpoints,
    list_directory_contents, list_projects, list_running_claude_sessions, load_session_history,
    open_new_session, read_claude_md_file, restore_checkpoint, resume_claude_code,
    save_claude_md_file, save_claude_settings, save_system_prompt, search_files,
    track_checkpoint_message, track_session_messages, update_checkpoint_settings,
    get_hooks_config, update_hooks_config, validate_hook_command,
    get_session_mode, switch_session_mode, enter_plan_mode, exit_plan_mode,
    get_auto_mode_triggers_enabled, suggest_mode_switch,
    ClaudeProcessState,
};
use commands::mcp::{
    mcp_add, mcp_add_from_claude_desktop, mcp_add_json, mcp_get, mcp_get_server_status, mcp_list,
    mcp_read_project_config, mcp_remove, mcp_reset_project_choices, mcp_save_project_config,
    mcp_serve, mcp_test_connection,
};
use commands::mcp_tools::{
    mcp_invoke_tool, mcp_list_tools, mcp_disconnect, mcp_disconnect_all,
};

use commands::usage::{
    get_session_stats, get_usage_by_date_range, get_usage_details, get_usage_stats,
};
use commands::storage::{
    storage_list_tables, storage_read_table, storage_update_row, storage_delete_row,
    storage_insert_row, storage_execute_sql, storage_reset_database,
};
use commands::proxy::{get_proxy_settings, save_proxy_settings, apply_proxy_settings};
use commands::plans::{
    extract_project_plans, get_project_plans, search_plans, update_plan_progress,
    mark_plan_referenced, get_plan_by_id, extract_session_plans,
};
use commands::quality::{
    get_quality_config, update_quality_config, detect_quality_tools, run_quality_validation,
    get_quality_history, export_quality_template,
};
use commands::mode_history::{
    record_mode_transition, get_session_mode_history, get_project_mode_history,
    get_mode_statistics, get_session_analytics, get_workflow_insights, ModeHistoryState,
};
use commands::mode_preferences::{
    get_mode_preferences, update_mode_preferences, update_mode_preference_field,
    export_mode_preferences, import_mode_preferences, reset_mode_preferences,
    ModePreferencesState,
};
use commands::analytics::{
    get_analytics_data, save_analytics_data, AnalyticsState,
};
use commands::claude_md_parser::{
    parse_claude_md, validate_claude_md, extract_claude_md_imports,
    generate_claude_md, update_claude_md_section, merge_claude_md_files,
    search_claude_md,
};
use commands::project_intelligence::{
    init_project_intelligence_db, execute_sql, query_sql,
    begin_transaction, commit_transaction, rollback_transaction,
    close_db, ProjectIntelligenceDb,
};
use commands::code_analysis::{
    init_code_analysis_schema, upsert_symbol, upsert_dependency,
    upsert_file_metadata, get_symbols_by_file, get_dependencies_by_source,
    delete_file_analysis, get_analysis_stats,
};
use commands::file_watcher::{
    start_file_watcher, stop_file_watcher, get_watcher_status, update_watcher_config,
    FileWatcherState,
};
use commands::parsing_queue::{
    start_parsing_queue, stop_parsing_queue, enqueue_parse_task, get_queue_status,
    get_parse_results, clear_parse_results, update_queue_config, ParsingQueueState,
};
use commands::analysis_coordinator::{
    start_analysis_session, stop_analysis_session, get_analysis_sessions,
    analyze_files, get_session_analysis_stats, AnalysisCoordinatorState,
};
use commands::parser_integration::{
    process_parse_task_with_parser, process_queue_with_parser,
};
use commands::cache::{
    init_cache, cache_get, cache_set, cache_delete, cache_exists,
    cache_clear, cache_get_many, cache_set_many, cache_get_stats,
    cache_close_all, CacheState,
};
use mode_history::ModeHistoryManager;
use mode_preferences::ModePreferencesManager;
use process::ProcessRegistryState;
use std::sync::{Arc, Mutex};
use tauri::Manager;

fn main() {
    // Initialize logger
    env_logger::init();


    tauri::Builder::default()
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .setup(|app| {
            // Initialize agents database
            let conn = init_database(&app.handle()).expect("Failed to initialize agents database");
            
            // Load and apply proxy settings from the database
            {
                let db = AgentDb(Mutex::new(conn));
                let proxy_settings = match db.0.lock() {
                    Ok(conn) => {
                        // Directly query proxy settings from the database
                        let mut settings = commands::proxy::ProxySettings::default();
                        
                        let keys = vec![
                            ("proxy_enabled", "enabled"),
                            ("proxy_http", "http_proxy"),
                            ("proxy_https", "https_proxy"),
                            ("proxy_no", "no_proxy"),
                            ("proxy_all", "all_proxy"),
                        ];
                        
                        for (db_key, field) in keys {
                            if let Ok(value) = conn.query_row(
                                "SELECT value FROM app_settings WHERE key = ?1",
                                rusqlite::params![db_key],
                                |row| row.get::<_, String>(0),
                            ) {
                                match field {
                                    "enabled" => settings.enabled = value == "true",
                                    "http_proxy" => settings.http_proxy = Some(value).filter(|s| !s.is_empty()),
                                    "https_proxy" => settings.https_proxy = Some(value).filter(|s| !s.is_empty()),
                                    "no_proxy" => settings.no_proxy = Some(value).filter(|s| !s.is_empty()),
                                    "all_proxy" => settings.all_proxy = Some(value).filter(|s| !s.is_empty()),
                                    _ => {}
                                }
                            }
                        }
                        
                        log::info!("Loaded proxy settings: enabled={}", settings.enabled);
                        settings
                    }
                    Err(e) => {
                        log::warn!("Failed to lock database for proxy settings: {}", e);
                        commands::proxy::ProxySettings::default()
                    }
                };
                
                // Apply the proxy settings
                apply_proxy_settings(&proxy_settings);
            }
            
            // Re-open the connection for the app to manage
            let conn = init_database(&app.handle()).expect("Failed to initialize agents database");
            app.manage(AgentDb(Mutex::new(conn)));

            // Initialize checkpoint state
            let checkpoint_state = CheckpointState::new();

            // Set the Claude directory path
            if let Ok(claude_dir) = dirs::home_dir()
                .ok_or_else(|| "Could not find home directory")
                .and_then(|home| {
                    let claude_path = home.join(".claude");
                    claude_path
                        .canonicalize()
                        .map_err(|_| "Could not find ~/.claude directory")
                })
            {
                let state_clone = checkpoint_state.clone();
                tauri::async_runtime::spawn(async move {
                    state_clone.set_claude_dir(claude_dir).await;
                });
            }

            app.manage(checkpoint_state);

            // Initialize process registry
            app.manage(ProcessRegistryState::default());

            // Initialize Claude process state
            app.manage(ClaudeProcessState::default());

            // Initialize mode history state
            let mode_history_manager = ModeHistoryManager::new();
            app.manage(ModeHistoryState(Arc::new(tokio::sync::Mutex::new(mode_history_manager))));

            // Initialize mode preferences state
            let app_data_dir = app.path().app_data_dir()
                .expect("Failed to get app data directory");
            let mode_preferences_manager = ModePreferencesManager::new(app_data_dir)
                .expect("Failed to initialize mode preferences");
            app.manage(ModePreferencesState(Arc::new(tokio::sync::Mutex::new(mode_preferences_manager))));

            // Initialize analytics state
            app.manage(AnalyticsState::new());
            
            // Initialize search database
            let search_conn = crate::search::db::init_database(&app.handle()).expect("Failed to initialize search database");
            app.manage(SearchDb(Mutex::new(search_conn)));
            
            // Initialize project intelligence database
            app.manage(ProjectIntelligenceDb::new());
            
            // Initialize cache state
            app.manage(CacheState::new());
            
            // Initialize file watcher state
            app.manage(FileWatcherState::default());
            
            // Initialize parsing queue state
            app.manage(ParsingQueueState::default());
            
            // Initialize analysis coordinator state
            app.manage(AnalysisCoordinatorState::default());
            
            // Initialize Crush state
            
            // Initialize Crush proxy state
            
            app.manage(app.handle().clone());
            
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // Claude & Project Management
            list_projects,
            get_project_sessions,
            get_claude_settings,
            open_new_session,
            get_system_prompt,
            check_claude_version,
            save_system_prompt,
            save_claude_settings,
            find_claude_md_files,
            read_claude_md_file,
            save_claude_md_file,
            load_session_history,
            execute_claude_code,
            continue_claude_code,
            resume_claude_code,
            cancel_claude_execution,
            list_running_claude_sessions,
            get_claude_session_output,
            list_directory_contents,
            search_files,
            get_recently_modified_files,
            get_hooks_config,
            update_hooks_config,
            validate_hook_command,
            
            // Mode Management
            get_session_mode,
            switch_session_mode,
            enter_plan_mode,
            exit_plan_mode,
            get_auto_mode_triggers_enabled,
            suggest_mode_switch,
            
            // Checkpoint Management
            create_checkpoint,
            restore_checkpoint,
            list_checkpoints,
            fork_from_checkpoint,
            get_session_timeline,
            update_checkpoint_settings,
            get_checkpoint_diff,
            track_checkpoint_message,
            track_session_messages,
            check_auto_checkpoint,
            cleanup_old_checkpoints,
            get_checkpoint_settings,
            clear_checkpoint_manager,
            get_checkpoint_state_stats,
            
            // Agent Management
            list_agents,
            create_agent,
            update_agent,
            delete_agent,
            get_agent,
            execute_agent,
            list_agent_runs,
            get_agent_run,
            list_agent_runs_with_metrics,
            get_agent_run_with_real_time_metrics,
            list_running_sessions,
            kill_agent_session,
            get_session_status,
            cleanup_finished_processes,
            get_session_output,
            get_live_session_output,
            stream_session_output,
            load_agent_session_history,
            get_claude_binary_path,
            set_claude_binary_path,
            list_claude_installations,
            export_agent,
            export_agent_to_file,
            import_agent,
            import_agent_from_file,
            fetch_github_agents,
            fetch_github_agent_content,
            import_agent_from_github,
            
            // Usage & Analytics
            get_usage_stats,
            get_usage_by_date_range,
            get_usage_details,
            get_session_stats,
            
            // MCP (Model Context Protocol)
            mcp_add,
            mcp_list,
            mcp_get,
            mcp_remove,
            mcp_add_json,
            mcp_add_from_claude_desktop,
            mcp_serve,
            mcp_test_connection,
            mcp_reset_project_choices,
            mcp_get_server_status,
            mcp_read_project_config,
            mcp_save_project_config,
            
            // MCP Tool Invocation
            mcp_invoke_tool,
            mcp_list_tools,
            mcp_disconnect,
            mcp_disconnect_all,
            
            // Storage Management
            storage_list_tables,
            storage_read_table,
            storage_update_row,
            storage_delete_row,
            storage_insert_row,
            storage_execute_sql,
            storage_reset_database,
            
            // Slash Commands
            commands::slash_commands::slash_commands_list,
            commands::slash_commands::slash_command_get,
            commands::slash_commands::slash_command_save,
            commands::slash_commands::slash_command_delete,
            
            // Proxy Settings
            get_proxy_settings,
            save_proxy_settings,
            
            // Plan Management
            extract_project_plans,
            get_project_plans,
            search_plans,
            update_plan_progress,
            mark_plan_referenced,
            get_plan_by_id,
            extract_session_plans,
            
            // Quality Validation
            get_quality_config,
            update_quality_config,
            detect_quality_tools,
            run_quality_validation,
            get_quality_history,
            export_quality_template,
            
            // Mode History
            record_mode_transition,
            get_session_mode_history,
            get_project_mode_history,
            get_mode_statistics,
            get_session_analytics,
            get_workflow_insights,
            
            // Mode Preferences
            get_mode_preferences,
            update_mode_preferences,
            update_mode_preference_field,
            export_mode_preferences,
            import_mode_preferences,
            reset_mode_preferences,
            
            // Analytics
            get_analytics_data,
            save_analytics_data,


            // CLAUDE.md Parser commands
            parse_claude_md,
            validate_claude_md,
            extract_claude_md_imports,
            generate_claude_md,
            update_claude_md_section,
            merge_claude_md_files,
            search_claude_md,

            // Search commands
            start_session_indexing,
            get_session_indexing_status,
            pause_session_indexing,
            
            // Project Intelligence commands
            init_project_intelligence_db,
            execute_sql,
            query_sql,
            begin_transaction,
            commit_transaction,
            rollback_transaction,
            close_db,
            
            // Cache commands
            init_cache,
            cache_get,
            cache_set,
            cache_delete,
            cache_exists,
            cache_clear,
            cache_get_many,
            cache_set_many,
            cache_get_stats,
            cache_close_all,
            
            // Code Analysis commands
            init_code_analysis_schema,
            upsert_symbol,
            upsert_dependency,
            upsert_file_metadata,
            get_symbols_by_file,
            get_dependencies_by_source,
            delete_file_analysis,
            get_analysis_stats,
            
            // File Watcher commands
            start_file_watcher,
            stop_file_watcher,
            get_watcher_status,
            update_watcher_config,
            
            // Parsing Queue commands
            start_parsing_queue,
            stop_parsing_queue,
            enqueue_parse_task,
            get_queue_status,
            get_parse_results,
            clear_parse_results,
            update_queue_config,
            
            // Analysis Coordinator commands
            start_analysis_session,
            stop_analysis_session,
            get_analysis_sessions,
            analyze_files,
            get_session_analysis_stats,
            
            // Parser Integration commands
            process_parse_task_with_parser,
            process_queue_with_parser,
            
            
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
