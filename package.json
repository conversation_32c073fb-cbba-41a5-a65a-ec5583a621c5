{"name": "claudia", "private": true, "version": "0.1.0", "license": "AGPL-3.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "check": "tsc --noEmit && cd src-tauri && cargo check", "test": "jest"}, "dependencies": {"@anthropic-ai/claude-code": "^1.0.55", "@babel/parser": "^7.28.0", "@babel/traverse": "^7.28.0", "@babel/types": "^7.28.1", "@codemirror/lang-markdown": "^6.2.3", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.22.2", "@hookform/resolvers": "^3.9.1", "@mongodb-js/zstd": "^2.0.1", "@msgpack/msgpack": "^3.1.2", "@node-rs/xxhash": "^1.7.6", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.1.3", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.3", "@radix-ui/react-tooltip": "^1.1.5", "@reduxjs/toolkit": "^2.8.2", "@tailwindcss/cli": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-virtual": "^3.13.10", "@tauri-apps/api": "^2.1.1", "@tauri-apps/plugin-dialog": "^2.0.2", "@tauri-apps/plugin-fs": "^2.0.2", "@tauri-apps/plugin-global-shortcut": "^2.0.0", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-shell": "^2.0.1", "@types/diff": "^8.0.0", "@types/glob": "^8.1.0", "@types/level": "^6.0.3", "@types/mdast": "^4.0.4", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "@uiw/codemirror-theme-vscode": "^4.21.21", "@uiw/react-codemirror": "^4.21.21", "@uiw/react-md-editor": "^4.0.7", "ansi-to-html": "^0.7.2", "better-sqlite3": "^12.2.0", "chokidar": "^3.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dexie": "^3.2.7", "diff": "^8.0.2", "file-saver": "^2.0.5", "framer-motion": "^12.0.0-alpha.1", "fuse.js": "^7.1.0", "glob": "^11.0.3", "html2canvas": "^1.4.1", "ignore": "^5.3.2", "jszip": "^3.10.1", "level": "^10.0.0", "lucide-react": "^0.468.0", "lz-string": "^1.5.0", "lz4": "^0.6.5", "react": "^18.3.1", "react-day-picker": "^9.8.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "react-markdown": "^9.0.3", "react-redux": "^9.2.0", "react-syntax-highlighter": "^15.6.1", "react-window": "^1.8.11", "recharts": "^2.14.1", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.1", "remark-parse": "^11.0.0", "remark-stringify": "^11.0.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.1.8", "tree-sitter": "^0.21.1", "tree-sitter-go": "^0.21.2", "tree-sitter-javascript": "^0.21.2", "tree-sitter-python": "^0.21.0", "tree-sitter-rust": "^0.21.0", "tree-sitter-typescript": "^0.21.2", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "web-tree-sitter": "^0.20.8", "yaml": "^2.8.0", "zod": "^3.24.1", "zustand": "^5.0.6"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/better-sqlite3": "^7.6.13", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/lz-string": "^1.5.0", "@types/lz4": "^0.6.4", "@types/node": "^22.15.30", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@types/sharp": "^0.32.0", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.3.4", "fake-indexeddb": "^6.0.1", "jest": "^29.7.0", "sharp": "^0.34.2", "ts-jest": "^29.4.0", "typescript": "^5.9.0-beta", "vite": "^6.0.3"}, "trustedDependencies": ["@parcel/watcher", "@tailwindcss/oxide"]}